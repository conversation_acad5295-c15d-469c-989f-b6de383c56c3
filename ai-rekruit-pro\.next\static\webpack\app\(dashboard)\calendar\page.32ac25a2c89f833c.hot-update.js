"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/calendar/page",{

/***/ "(app-pages-browser)/./src/components/layout/Layout.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Layout.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Layout = (param)=>{\n    let { children, title, showSearch = true, onSearch, headerActions, className } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Default sidebar navigation items\n    const sidebarItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: 'dashboard',\n            href: '/dashboard',\n            description: 'Overview and key metrics'\n        },\n        {\n            id: 'jobs',\n            label: 'Jobs',\n            icon: 'jobs',\n            href: '/jobs',\n            badge: '5',\n            description: 'Manage job postings'\n        },\n        {\n            id: 'candidates',\n            label: 'Candidates',\n            icon: 'candidates',\n            href: '/candidates',\n            description: 'View and manage candidates'\n        },\n        {\n            id: 'resume-analysis',\n            label: 'Resume Analysis',\n            icon: 'analytics',\n            href: '/resume-analysis',\n            description: 'AI-powered resume analysis'\n        },\n        {\n            id: 'analytics',\n            label: 'Analytics',\n            icon: 'analytics',\n            href: '/analytics',\n            description: 'Recruitment analytics and reports'\n        },\n        {\n            id: 'calendar',\n            label: 'Calendar',\n            icon: 'calendar',\n            href: '/calendar',\n            description: 'Schedule and manage interviews'\n        },\n        {\n            id: 'profile',\n            label: 'Profile',\n            icon: 'settings',\n            href: '/profile',\n            description: 'Your profile settings'\n        },\n        {\n            id: 'settings',\n            label: 'Settings',\n            icon: 'settings',\n            href: '/settings',\n            description: 'Application settings'\n        }\n    ];\n    // Get page title based on current route\n    const getPageTitle = ()=>{\n        if (title) return title;\n        const currentItem = sidebarItems.find((item)=>{\n            if (item.href === '/dashboard' && pathname === '/dashboard') {\n                return true;\n            }\n            if (item.href !== '/dashboard' && pathname.startsWith(item.href)) {\n                return true;\n            }\n            return false;\n        });\n        return (currentItem === null || currentItem === void 0 ? void 0 : currentItem.label) || 'AI RecruitPro';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    items: sidebarItems\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: getPageTitle(),\n                        showSearch: showSearch,\n                        onSearch: onSearch,\n                        actions: headerActions\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex-1 overflow-auto p-6', className),\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = Layout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Layout.tsx\n"));

/***/ })

});