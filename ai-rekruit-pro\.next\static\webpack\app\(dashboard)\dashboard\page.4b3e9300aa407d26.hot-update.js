"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/JobPostingCard.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/JobPostingCard.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst JobPostingCard = (param)=>{\n    let { job, onView, onEdit, onDelete, className } = param;\n    _s();\n    const handleView = ()=>onView === null || onView === void 0 ? void 0 : onView(job);\n    const handleEdit = ()=>onEdit === null || onEdit === void 0 ? void 0 : onEdit(job);\n    const handleDelete = ()=>onDelete === null || onDelete === void 0 ? void 0 : onDelete(job);\n    const [formattedSalary, setFormattedSalary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JobPostingCard.useEffect\": ()=>{\n            if (job.salary) {\n                const formatted = \"\".concat(job.salary.currency, \" \").concat(job.salary.min.toLocaleString('en-US'), \" - \").concat(job.salary.max.toLocaleString('en-US'));\n                setFormattedSalary(formatted);\n            }\n        }\n    }[\"JobPostingCard.useEffect\"], [\n        job.salary\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('hover:shadow-md transition-shadow', className),\n        padding: \"md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                children: job.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2\",\n                                children: [\n                                    job.department,\n                                    \" • \",\n                                    job.location\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: job.status === 'active' ? 'success' : job.status === 'paused' ? 'warning' : job.status === 'closed' ? 'danger' : 'default',\n                                        size: \"sm\",\n                                        children: job.status\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"info\",\n                                        size: \"sm\",\n                                        children: job.type\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleView,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                    name: \"eye\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleEdit,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                    name: \"edit\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleDelete,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                    name: \"trash\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-700 line-clamp-2\",\n                        children: job.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    formattedSalary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: formattedSalary\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between text-sm text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Posted \",\n                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatRelativeTime)(job.postedDate)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    job.closingDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Closes \",\n                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatRelativeTime)(job.closingDate)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_s(JobPostingCard, \"HPgYjVxwtkb4F+2mGb75C5Nquq8=\");\n_c = JobPostingCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JobPostingCard);\nvar _c;\n$RefreshReg$(_c, \"JobPostingCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/JobPostingCard.tsx\n"));

/***/ })

});