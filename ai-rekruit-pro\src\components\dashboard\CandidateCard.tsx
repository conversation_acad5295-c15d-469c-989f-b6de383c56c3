import React from 'react';
import { cn, formatRelativeTime } from '@/lib/utils';
import { Card, Badge, Button, Icon } from '@/components/ui';
import { Candidate } from '@/types';

interface CandidateCardProps {
  candidate: Candidate;
  onView?: (candidate: Candidate) => void;
  onContact?: (candidate: Candidate) => void;
  onScheduleInterview?: (candidate: Candidate) => void;
  className?: string;
}

const CandidateCard: React.FC<CandidateCardProps> = ({
  candidate,
  onView,
  onContact,
  onScheduleInterview,
  className,
}) => {
  const handleView = () => onView?.(candidate);
  const handleContact = () => onContact?.(candidate);
  const handleScheduleInterview = () => onScheduleInterview?.(candidate);

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'new': return 'info';
      case 'screening': return 'warning';
      case 'interview': return 'default';
      case 'offer': return 'success';
      case 'hired': return 'success';
      case 'rejected': return 'danger';
      default: return 'default';
    }
  };

  return (
    <Card className={cn('hover:shadow-md transition-shadow', className)} padding="md">
      <div className="flex items-start space-x-4">
        {/* Avatar */}
        <div className="flex-shrink-0">
          {candidate.avatar ? (
            <img
              src={candidate.avatar}
              alt={candidate.name}
              className="w-12 h-12 rounded-full object-cover"
            />
          ) : (
            <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-gray-600 font-medium text-lg">
                {candidate.name.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {candidate.name}
              </h3>
              <p className="text-sm text-gray-600">
                {candidate.email}
              </p>
              {candidate.phone && (
                <p className="text-sm text-gray-600">
                  {candidate.phone}
                </p>
              )}
            </div>
            
            <Badge 
              variant={getStatusVariant(candidate.status)}
              size="sm"
            >
              {candidate.status}
            </Badge>
          </div>

          <div className="space-y-2 mb-4">
            <div className="flex items-center text-sm text-gray-600">
              <Icon name="location" size="sm" className="mr-1" />
              <span>{candidate.location}</span>
              <span className="mx-2">•</span>
              <span>{candidate.experience} years experience</span>
            </div>
            
            {candidate.skills.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {candidate.skills.slice(0, 3).map((skill, index) => (
                  <Badge key={index} variant="default" size="sm">
                    {skill}
                  </Badge>
                ))}
                {candidate.skills.length > 3 && (
                  <Badge variant="default" size="sm">
                    +{candidate.skills.length - 3} more
                  </Badge>
                )}
              </div>
            )}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">
              Applied {formatRelativeTime(candidate.appliedDate)}
            </span>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleView}
              >
                <Icon name="eye" size="sm" className="mr-1" />
                View
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleContact}
              >
                <Icon name="mail" size="sm" className="mr-1" />
                Contact
              </Button>
              {candidate.status === 'screening' && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleScheduleInterview}
                >
                  <Icon name="calendar" size="sm" className="mr-1" />
                  Interview
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CandidateCard;
