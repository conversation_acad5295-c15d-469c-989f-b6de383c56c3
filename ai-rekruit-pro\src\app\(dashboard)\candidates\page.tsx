'use client';

import React from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>Bar, 
  FilterDropdown, 
  DataTable, 
  Badge,
  CandidateCard
} from '@/components';
import { Candidate } from '@/types';

export default function CandidatesPage() {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [statusFilters, setStatusFilters] = React.useState<string[]>([]);
  const [skillFilters, setSkillFilters] = React.useState<string[]>([]);

  // Sample candidate data
  const candidates: Candidate[] = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'screening',
      appliedJobs: ['1'],
      skills: ['React', 'TypeScript', 'Node.js', 'Python'],
      experience: 5,
      location: 'San Francisco, CA',
      appliedDate: new Date('2024-01-20')
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'interview',
      appliedJobs: ['1', '3'],
      skills: ['JavaScript', 'React', 'AWS', 'Docker'],
      experience: 7,
      location: 'Seattle, WA',
      appliedDate: new Date('2024-01-18')
    },
    {
      id: '3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'offer',
      appliedJobs: ['2'],
      skills: ['Figma', 'Adobe XD', 'User Research', 'Prototyping'],
      experience: 4,
      location: 'Austin, TX',
      appliedDate: new Date('2024-01-15')
    },
    {
      id: '4',
      name: 'David Kim',
      email: '<EMAIL>',
      status: 'hired',
      appliedJobs: ['3'],
      skills: ['Python', 'Django', 'PostgreSQL', 'Redis'],
      experience: 6,
      location: 'New York, NY',
      appliedDate: new Date('2024-01-10')
    }
  ];

  const statusOptions = [
    { value: 'new', label: 'New', count: 0 },
    { value: 'screening', label: 'Screening', count: 1 },
    { value: 'interview', label: 'Interview', count: 1 },
    { value: 'offer', label: 'Offer', count: 1 },
    { value: 'hired', label: 'Hired', count: 1 },
    { value: 'rejected', label: 'Rejected', count: 0 }
  ];

  const skillOptions = [
    { value: 'react', label: 'React', count: 2 },
    { value: 'typescript', label: 'TypeScript', count: 1 },
    { value: 'python', label: 'Python', count: 2 },
    { value: 'figma', label: 'Figma', count: 1 },
    { value: 'aws', label: 'AWS', count: 1 }
  ];

  const tableColumns = [
    { key: 'name', title: 'Name', sortable: true },
    { key: 'email', title: 'Email', sortable: true },
    { key: 'location', title: 'Location', sortable: true },
    { key: 'experience', title: 'Experience', sortable: true },
    { 
      key: 'status', 
      title: 'Status', 
      render: (value: string) => (
        <Badge variant={
          value === 'hired' ? 'success' : 
          value === 'offer' ? 'success' : 
          value === 'interview' ? 'warning' : 
          'default'
        }>
          {value}
        </Badge>
      )
    },
    { key: 'appliedDate', title: 'Applied Date', sortable: true }
  ];

  const tableData = candidates.map(candidate => ({
    ...candidate,
    experience: `${candidate.experience} years`,
    appliedDate: candidate.appliedDate.toLocaleDateString()
  }));

  const handleCandidateAction = (action: string, candidate: Candidate) => {
    console.log(`${action} candidate:`, candidate);
  };

  const handleBulkUpload = () => {
    console.log('Bulk upload resumes');
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Candidates</h1>
          <p className="text-gray-600">Manage and review candidate applications</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={handleBulkUpload}>
            Bulk Upload
          </Button>
          <Button variant="primary">
            Add Candidate
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card padding="md">
        <div className="space-y-4">
          <SearchBar
            placeholder="Search candidates by name, email, or skills..."
            value={searchQuery}
            onChange={setSearchQuery}
            onSearch={(query) => console.log('Search:', query)}
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FilterDropdown
              title="Status"
              options={statusOptions}
              selectedValues={statusFilters}
              onChange={setStatusFilters}
              placeholder="Filter by status"
            />
            <FilterDropdown
              title="Skills"
              options={skillOptions}
              selectedValues={skillFilters}
              onChange={setSkillFilters}
              placeholder="Filter by skills"
            />
          </div>
        </div>
      </Card>

      {/* Candidate Cards */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Applications</h2>
        <div className="space-y-4">
          {candidates.slice(0, 3).map((candidate) => (
            <CandidateCard
              key={candidate.id}
              candidate={candidate}
              onView={(candidate) => handleCandidateAction('view', candidate)}
              onContact={(candidate) => handleCandidateAction('contact', candidate)}
              onScheduleInterview={(candidate) => handleCandidateAction('schedule', candidate)}
            />
          ))}
        </div>
      </div>

      {/* Candidates Table */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">All Candidates</h2>
        <DataTable
          columns={tableColumns}
          data={tableData}
          onSort={(key, direction) => console.log('Sort:', key, direction)}
          onRowClick={(row) => console.log('Row clicked:', row)}
        />
      </div>
    </div>
  );
}
