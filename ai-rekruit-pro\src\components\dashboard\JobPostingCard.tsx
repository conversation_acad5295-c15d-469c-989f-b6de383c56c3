import React from 'react';
import { cn, formatRelativeTime, getStatusColor } from '@/lib/utils';
import { Card, Badge, Button, Icon } from '@/components/ui';
import { Job } from '@/types';

interface JobPostingCardProps {
  job: Job;
  onView?: (job: Job) => void;
  onEdit?: (job: Job) => void;
  onDelete?: (job: Job) => void;
  className?: string;
}

const JobPostingCard: React.FC<JobPostingCardProps> = ({
  job,
  onView,
  onEdit,
  onDelete,
  className,
}) => {
  const handleView = () => onView?.(job);
  const handleEdit = () => onEdit?.(job);
  const handleDelete = () => onDelete?.(job);

  return (
    <Card className={cn('hover:shadow-md transition-shadow', className)} padding="md">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {job.title}
          </h3>
          <p className="text-sm text-gray-600 mb-2">
            {job.department} • {job.location}
          </p>
          <div className="flex items-center space-x-2">
            <Badge 
              variant={job.status === 'active' ? 'success' : 
                      job.status === 'paused' ? 'warning' : 
                      job.status === 'closed' ? 'danger' : 'default'}
              size="sm"
            >
              {job.status}
            </Badge>
            <Badge variant="info" size="sm">
              {job.type}
            </Badge>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleView}
          >
            <Icon name="eye" size="sm" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEdit}
          >
            <Icon name="edit" size="sm" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
          >
            <Icon name="trash" size="sm" />
          </Button>
        </div>
      </div>

      <div className="space-y-2 mb-4">
        <p className="text-sm text-gray-700 line-clamp-2">
          {job.description}
        </p>
        
        {job.salary && (
          <p className="text-sm font-medium text-gray-900">
            {job.salary.currency} {job.salary.min.toLocaleString()} - {job.salary.max.toLocaleString()}
          </p>
        )}
      </div>

      <div className="flex items-center justify-between text-sm text-gray-500">
        <span>Posted {formatRelativeTime(job.postedDate)}</span>
        {job.closingDate && (
          <span>Closes {formatRelativeTime(job.closingDate)}</span>
        )}
      </div>
    </Card>
  );
};

export default JobPostingCard;
