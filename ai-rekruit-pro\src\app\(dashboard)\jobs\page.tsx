'use client';

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  SearchBar,
  FilterDropdown,
  DataTable,
  Badge,
  JobPostingCard
} from '@/components';
import { Job } from '@/types';
import { formatDateConsistent } from '@/lib/utils';

export default function JobsPage() {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [statusFilters, setStatusFilters] = React.useState<string[]>([]);
  const [departmentFilters, setDepartmentFilters] = React.useState<string[]>([]);

  // Sample job data
  const jobs: Job[] = [
    {
      id: '1',
      title: 'Senior Frontend Developer',
      department: 'Engineering',
      location: 'San Francisco, CA',
      type: 'full-time',
      status: 'active',
      description: 'We are looking for an experienced frontend developer to join our team and help build amazing user experiences.',
      requirements: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS'],
      postedDate: new Date('2024-01-15'),
      closingDate: new Date('2024-02-15'),
      salary: { min: 120000, max: 160000, currency: 'USD' }
    },
    {
      id: '2',
      title: 'UX Designer',
      department: 'Design',
      location: 'Remote',
      type: 'full-time',
      status: 'paused',
      description: 'Join our design team to create intuitive and beautiful user interfaces.',
      requirements: ['Figma', 'Adobe Creative Suite', 'User Research', 'Prototyping'],
      postedDate: new Date('2024-01-10'),
      closingDate: new Date('2024-02-10'),
      salary: { min: 90000, max: 120000, currency: 'USD' }
    },
    {
      id: '3',
      title: 'Backend Engineer',
      department: 'Engineering',
      location: 'New York, NY',
      type: 'full-time',
      status: 'active',
      description: 'Build scalable backend systems and APIs for our growing platform.',
      requirements: ['Node.js', 'Python', 'PostgreSQL', 'AWS'],
      postedDate: new Date('2024-01-20'),
      closingDate: new Date('2024-02-20'),
      salary: { min: 130000, max: 170000, currency: 'USD' }
    }
  ];

  const statusOptions = [
    { value: 'active', label: 'Active', count: 2 },
    { value: 'paused', label: 'Paused', count: 1 },
    { value: 'closed', label: 'Closed', count: 0 },
    { value: 'draft', label: 'Draft', count: 0 }
  ];

  const departmentOptions = [
    { value: 'engineering', label: 'Engineering', count: 2 },
    { value: 'design', label: 'Design', count: 1 },
    { value: 'marketing', label: 'Marketing', count: 0 },
    { value: 'sales', label: 'Sales', count: 0 }
  ];

  const tableColumns = [
    { key: 'title', title: 'Job Title', sortable: true },
    { key: 'department', title: 'Department', sortable: true },
    { key: 'location', title: 'Location', sortable: true },
    { 
      key: 'status', 
      title: 'Status', 
      render: (value: string) => (
        <Badge variant={value === 'active' ? 'success' : value === 'paused' ? 'warning' : 'default'}>
          {value}
        </Badge>
      )
    },
    { key: 'postedDate', title: 'Posted Date', sortable: true }
  ];

  const tableData = jobs.map(job => ({
    ...job,
    postedDate: formatDateConsistent(job.postedDate)
  }));

  const handleCreateJob = () => {
    console.log('Create new job');
  };

  const handleJobAction = (action: string, job: Job) => {
    console.log(`${action} job:`, job);
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Job Postings</h1>
          <p className="text-gray-600">Manage and track your job openings</p>
        </div>
        <Button variant="primary" onClick={handleCreateJob}>
          Post New Job
        </Button>
      </div>

      {/* Search and Filters */}
      <Card padding="md">
        <div className="space-y-4">
          <SearchBar
            placeholder="Search jobs by title, department, or location..."
            value={searchQuery}
            onChange={setSearchQuery}
            onSearch={(query) => console.log('Search:', query)}
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FilterDropdown
              title="Status"
              options={statusOptions}
              selectedValues={statusFilters}
              onChange={setStatusFilters}
              placeholder="Filter by status"
            />
            <FilterDropdown
              title="Department"
              options={departmentOptions}
              selectedValues={departmentFilters}
              onChange={setDepartmentFilters}
              placeholder="Filter by department"
            />
          </div>
        </div>
      </Card>

      {/* Job Cards Grid */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Active Postings</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {jobs.map((job) => (
            <JobPostingCard
              key={job.id}
              job={job}
              onView={(job) => handleJobAction('view', job)}
              onEdit={(job) => handleJobAction('edit', job)}
              onDelete={(job) => handleJobAction('delete', job)}
            />
          ))}
        </div>
      </div>

      {/* Jobs Table */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">All Jobs</h2>
        <DataTable
          columns={tableColumns}
          data={tableData}
          onSort={(key, direction) => console.log('Sort:', key, direction)}
          onRowClick={(row) => console.log('Row clicked:', row)}
        />
      </div>
    </div>
  );
}
