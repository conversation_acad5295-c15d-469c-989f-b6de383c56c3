'use client';

import React from 'react';
import { 
  Card, 
  Button,
  FormField,
  Icon
} from '@/components';

export default function ProfilePage() {
  const [isEditing, setIsEditing] = React.useState(false);
  const [formData, setFormData] = React.useState({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    title: 'HR Manager',
    department: 'Human Resources',
    location: 'San Francisco, CA',
    bio: 'Experienced HR professional with 8+ years in talent acquisition and employee development.',
    timezone: 'America/Los_Angeles',
    language: 'English'
  });

  const handleFieldChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = () => {
    console.log('Save profile:', formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset form data to original values
  };

  const handleChangePassword = () => {
    console.log('Change password');
  };

  const handleUploadAvatar = () => {
    console.log('Upload avatar');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Profile</h1>
          <p className="text-gray-600">Manage your account settings and preferences</p>
        </div>
        {!isEditing ? (
          <Button variant="primary" onClick={() => setIsEditing(true)}>
            Edit Profile
          </Button>
        ) : (
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button variant="primary" onClick={handleSave}>
              Save Changes
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Picture */}
        <div className="lg:col-span-1">
          <Card title="Profile Picture" padding="lg">
            <div className="text-center">
              <div className="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-gray-600 text-3xl font-medium">
                  {formData.firstName[0]}{formData.lastName[0]}
                </span>
              </div>
              <Button variant="outline" onClick={handleUploadAvatar}>
                <Icon name="upload" size="sm" className="mr-2" />
                Upload Photo
              </Button>
              <p className="text-sm text-gray-500 mt-2">
                JPG, PNG or GIF. Max size 2MB.
              </p>
            </div>
          </Card>

          {/* Quick Stats */}
          <Card title="Quick Stats" padding="lg" className="mt-6">
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Jobs Posted</span>
                <span className="font-semibold">23</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Candidates Reviewed</span>
                <span className="font-semibold">156</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Interviews Scheduled</span>
                <span className="font-semibold">42</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Successful Hires</span>
                <span className="font-semibold">18</span>
              </div>
            </div>
          </Card>
        </div>

        {/* Profile Information */}
        <div className="lg:col-span-2">
          <Card title="Personal Information" padding="lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                label="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleFieldChange}
                disabled={!isEditing}
                required
              />
              <FormField
                label="Last Name"
                name="lastName"
                value={formData.lastName}
                onChange={handleFieldChange}
                disabled={!isEditing}
                required
              />
              <FormField
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleFieldChange}
                disabled={!isEditing}
                required
              />
              <FormField
                label="Phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleFieldChange}
                disabled={!isEditing}
              />
              <FormField
                label="Job Title"
                name="title"
                value={formData.title}
                onChange={handleFieldChange}
                disabled={!isEditing}
              />
              <FormField
                label="Department"
                name="department"
                value={formData.department}
                onChange={handleFieldChange}
                disabled={!isEditing}
              />
              <FormField
                label="Location"
                name="location"
                value={formData.location}
                onChange={handleFieldChange}
                disabled={!isEditing}
              />
              <FormField
                label="Timezone"
                name="timezone"
                type="select"
                value={formData.timezone}
                onChange={handleFieldChange}
                disabled={!isEditing}
                options={[
                  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
                  { value: 'America/Denver', label: 'Mountain Time (MT)' },
                  { value: 'America/Chicago', label: 'Central Time (CT)' },
                  { value: 'America/New_York', label: 'Eastern Time (ET)' }
                ]}
              />
            </div>
            
            <div className="mt-6">
              <FormField
                label="Bio"
                name="bio"
                type="textarea"
                value={formData.bio}
                onChange={handleFieldChange}
                disabled={!isEditing}
                rows={4}
                helpText="Tell us a bit about yourself and your role"
              />
            </div>
          </Card>

          {/* Security Settings */}
          <Card title="Security" padding="lg" className="mt-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium text-gray-900">Password</p>
                  <p className="text-sm text-gray-600">Last changed 3 months ago</p>
                </div>
                <Button variant="outline" onClick={handleChangePassword}>
                  Change Password
                </Button>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-gray-900">Two-Factor Authentication</p>
                    <p className="text-sm text-gray-600">Add an extra layer of security</p>
                  </div>
                  <Button variant="outline">
                    Enable 2FA
                  </Button>
                </div>
              </div>
            </div>
          </Card>

          {/* Preferences */}
          <Card title="Preferences" padding="lg" className="mt-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium text-gray-900">Email Notifications</p>
                  <p className="text-sm text-gray-600">Receive updates about new applications</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
              
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium text-gray-900">Browser Notifications</p>
                  <p className="text-sm text-gray-600">Get notified in your browser</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
