globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(dashboard)/profile/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/(dashboard)/layout.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/jobs/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/jobs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/candidates/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/candidates/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/resume-analysis/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/resume-analysis/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/analytics/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/calendar/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/calendar/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/profile/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/settings/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\layout.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/layout.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\jobs\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/jobs/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\candidates\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/candidates/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\resume-analysis\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/resume-analysis/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\analytics\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/analytics/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\calendar\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/calendar/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\profile\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/profile/page.tsx","name":"*","chunks":["app/(dashboard)/profile/page","static/chunks/app/(dashboard)/profile/page.js"],"async":false},"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\settings\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\":[],"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\layout":[],"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\profile\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/layout.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/jobs/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/jobs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/candidates/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/candidates/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/resume-analysis/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/resume-analysis/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/analytics/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/calendar/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/calendar/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/profile/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/settings/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}