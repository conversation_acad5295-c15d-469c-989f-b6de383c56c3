import React from 'react';
import { cn } from '@/lib/utils';
import Sidebar from './Sidebar';
import Header from './Header';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  showSearch?: boolean;
  onSearch?: (query: string) => void;
  headerActions?: React.ReactNode;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  title,
  showSearch = true,
  onSearch,
  headerActions,
  className,
}) => {
  // Default sidebar navigation items
  const sidebarItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'dashboard',
      href: '/dashboard',
      active: true,
    },
    {
      id: 'jobs',
      label: 'Jobs',
      icon: 'jobs',
      href: '/jobs',
      badge: '5',
    },
    {
      id: 'candidates',
      label: 'Candidates',
      icon: 'candidates',
      href: '/candidates',
    },
    {
      id: 'resume-analysis',
      label: 'Resume Analysis',
      icon: 'analytics',
      href: '/resume-analysis',
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: 'analytics',
      href: '/analytics',
    },
    {
      id: 'calendar',
      label: 'Calendar',
      icon: 'calendar',
      href: '/calendar',
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: 'settings',
      href: '/profile',
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: 'settings',
      href: '/settings',
    },
  ];

  const handleSidebarItemClick = (item: any) => {
    // Handle navigation - in a real app, this would use Next.js router
    console.log('Navigate to:', item.href);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="flex-shrink-0">
        <Sidebar 
          items={sidebarItems} 
          onItemClick={handleSidebarItemClick}
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header
          title={title}
          showSearch={showSearch}
          onSearch={onSearch}
          actions={headerActions}
        />

        {/* Page Content */}
        <main className={cn('flex-1 overflow-auto p-6', className)}>
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
