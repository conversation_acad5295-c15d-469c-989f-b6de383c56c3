"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/candidates/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/candidates/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/(dashboard)/candidates/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CandidatesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components */ \"(app-pages-browser)/./src/components/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CandidatesPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('');\n    const [statusFilters, setStatusFilters] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const [skillFilters, setSkillFilters] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    // Sample candidate data\n    const candidates = [\n        {\n            id: '1',\n            name: 'Sarah Johnson',\n            email: '<EMAIL>',\n            phone: '+****************',\n            status: 'screening',\n            appliedJobs: [\n                '1'\n            ],\n            skills: [\n                'React',\n                'TypeScript',\n                'Node.js',\n                'Python'\n            ],\n            experience: 5,\n            location: 'San Francisco, CA',\n            appliedDate: new Date('2024-01-20')\n        },\n        {\n            id: '2',\n            name: 'Mike Chen',\n            email: '<EMAIL>',\n            phone: '+****************',\n            status: 'interview',\n            appliedJobs: [\n                '1',\n                '3'\n            ],\n            skills: [\n                'JavaScript',\n                'React',\n                'AWS',\n                'Docker'\n            ],\n            experience: 7,\n            location: 'Seattle, WA',\n            appliedDate: new Date('2024-01-18')\n        },\n        {\n            id: '3',\n            name: 'Emily Rodriguez',\n            email: '<EMAIL>',\n            phone: '+****************',\n            status: 'offer',\n            appliedJobs: [\n                '2'\n            ],\n            skills: [\n                'Figma',\n                'Adobe XD',\n                'User Research',\n                'Prototyping'\n            ],\n            experience: 4,\n            location: 'Austin, TX',\n            appliedDate: new Date('2024-01-15')\n        },\n        {\n            id: '4',\n            name: 'David Kim',\n            email: '<EMAIL>',\n            status: 'hired',\n            appliedJobs: [\n                '3'\n            ],\n            skills: [\n                'Python',\n                'Django',\n                'PostgreSQL',\n                'Redis'\n            ],\n            experience: 6,\n            location: 'New York, NY',\n            appliedDate: new Date('2024-01-10')\n        }\n    ];\n    const statusOptions = [\n        {\n            value: 'new',\n            label: 'New',\n            count: 0\n        },\n        {\n            value: 'screening',\n            label: 'Screening',\n            count: 1\n        },\n        {\n            value: 'interview',\n            label: 'Interview',\n            count: 1\n        },\n        {\n            value: 'offer',\n            label: 'Offer',\n            count: 1\n        },\n        {\n            value: 'hired',\n            label: 'Hired',\n            count: 1\n        },\n        {\n            value: 'rejected',\n            label: 'Rejected',\n            count: 0\n        }\n    ];\n    const skillOptions = [\n        {\n            value: 'react',\n            label: 'React',\n            count: 2\n        },\n        {\n            value: 'typescript',\n            label: 'TypeScript',\n            count: 1\n        },\n        {\n            value: 'python',\n            label: 'Python',\n            count: 2\n        },\n        {\n            value: 'figma',\n            label: 'Figma',\n            count: 1\n        },\n        {\n            value: 'aws',\n            label: 'AWS',\n            count: 1\n        }\n    ];\n    const tableColumns = [\n        {\n            key: 'name',\n            title: 'Name',\n            sortable: true\n        },\n        {\n            key: 'email',\n            title: 'Email',\n            sortable: true\n        },\n        {\n            key: 'location',\n            title: 'Location',\n            sortable: true\n        },\n        {\n            key: 'experience',\n            title: 'Experience',\n            sortable: true\n        },\n        {\n            key: 'status',\n            title: 'Status',\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                    variant: value === 'hired' ? 'success' : value === 'offer' ? 'success' : value === 'interview' ? 'warning' : 'default',\n                    children: value\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'appliedDate',\n            title: 'Applied Date',\n            sortable: true\n        }\n    ];\n    const tableData = candidates.map((candidate)=>({\n            ...candidate,\n            experience: \"\".concat(candidate.experience, \" years\"),\n            appliedDate: candidate.appliedDate.toLocaleDateString()\n        }));\n    const handleCandidateAction = (action, candidate)=>{\n        console.log(\"\".concat(action, \" candidate:\"), candidate);\n    };\n    const handleBulkUpload = ()=>{\n        console.log('Bulk upload resumes');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Candidates\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage and review candidate applications\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkUpload,\n                                children: \"Bulk Upload\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"primary\",\n                                children: \"Add Candidate\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                padding: \"md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.SearchBar, {\n                            placeholder: \"Search candidates by name, email, or skills...\",\n                            value: searchQuery,\n                            onChange: setSearchQuery,\n                            onSearch: (query)=>console.log('Search:', query)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FilterDropdown, {\n                                    title: \"Status\",\n                                    options: statusOptions,\n                                    selectedValues: statusFilters,\n                                    onChange: setStatusFilters,\n                                    placeholder: \"Filter by status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FilterDropdown, {\n                                    title: \"Skills\",\n                                    options: skillOptions,\n                                    selectedValues: skillFilters,\n                                    onChange: setSkillFilters,\n                                    placeholder: \"Filter by skills\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Recent Applications\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: candidates.slice(0, 3).map((candidate)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.CandidateCard, {\n                                candidate: candidate,\n                                onView: (candidate)=>handleCandidateAction('view', candidate),\n                                onContact: (candidate)=>handleCandidateAction('contact', candidate),\n                                onScheduleInterview: (candidate)=>handleCandidateAction('schedule', candidate)\n                            }, candidate.id, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"All Candidates\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                        columns: tableColumns,\n                        data: tableData,\n                        onSort: (key, direction)=>console.log('Sort:', key, direction),\n                        onRowClick: (row)=>console.log('Row clicked:', row)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(CandidatesPage, \"CbqPk5TRgdqZ6snlQjcu+Fv8lqs=\");\n_c = CandidatesPage;\nvar _c;\n$RefreshReg$(_c, \"CandidatesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/candidates/page.tsx\n"));

/***/ })

});