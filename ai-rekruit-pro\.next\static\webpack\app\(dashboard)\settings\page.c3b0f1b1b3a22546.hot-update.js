"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(dashboard)/settings/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components */ \"(app-pages-browser)/./src/components/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SettingsPage() {\n    _s();\n    const [activeTab, setActiveTab] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('general');\n    const [generalSettings, setGeneralSettings] = react__WEBPACK_IMPORTED_MODULE_1___default().useState({\n        companyName: 'TechCorp Inc.',\n        companyWebsite: 'https://techcorp.com',\n        companySize: '100-500',\n        industry: 'Technology',\n        defaultCurrency: 'USD',\n        dateFormat: 'MM/DD/YYYY',\n        timeFormat: '12-hour'\n    });\n    const [integrationSettings, setIntegrationSettings] = react__WEBPACK_IMPORTED_MODULE_1___default().useState({\n        linkedinEnabled: true,\n        indeedEnabled: true,\n        glassdoorEnabled: false,\n        slackEnabled: true,\n        emailProvider: 'gmail'\n    });\n    const tabs = [\n        {\n            id: 'general',\n            label: 'General',\n            icon: 'settings'\n        },\n        {\n            id: 'integrations',\n            label: 'Integrations',\n            icon: 'link'\n        },\n        {\n            id: 'notifications',\n            label: 'Notifications',\n            icon: 'bell'\n        },\n        {\n            id: 'security',\n            label: 'Security',\n            icon: 'shield'\n        },\n        {\n            id: 'billing',\n            label: 'Billing',\n            icon: 'credit-card'\n        }\n    ];\n    const handleGeneralChange = (name, value)=>{\n        setGeneralSettings((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSaveSettings = ()=>{\n        console.log('Save settings');\n    };\n    const renderGeneralSettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Company Information\",\n                    padding: \"lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FormField, {\n                                label: \"Company Name\",\n                                name: \"companyName\",\n                                value: generalSettings.companyName,\n                                onChange: handleGeneralChange,\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FormField, {\n                                label: \"Website\",\n                                name: \"companyWebsite\",\n                                type: \"url\",\n                                value: generalSettings.companyWebsite,\n                                onChange: handleGeneralChange\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FormField, {\n                                label: \"Company Size\",\n                                name: \"companySize\",\n                                type: \"select\",\n                                value: generalSettings.companySize,\n                                onChange: handleGeneralChange,\n                                options: [\n                                    {\n                                        value: '1-10',\n                                        label: '1-10 employees'\n                                    },\n                                    {\n                                        value: '11-50',\n                                        label: '11-50 employees'\n                                    },\n                                    {\n                                        value: '51-100',\n                                        label: '51-100 employees'\n                                    },\n                                    {\n                                        value: '100-500',\n                                        label: '100-500 employees'\n                                    },\n                                    {\n                                        value: '500+',\n                                        label: '500+ employees'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FormField, {\n                                label: \"Industry\",\n                                name: \"industry\",\n                                type: \"select\",\n                                value: generalSettings.industry,\n                                onChange: handleGeneralChange,\n                                options: [\n                                    {\n                                        value: 'Technology',\n                                        label: 'Technology'\n                                    },\n                                    {\n                                        value: 'Healthcare',\n                                        label: 'Healthcare'\n                                    },\n                                    {\n                                        value: 'Finance',\n                                        label: 'Finance'\n                                    },\n                                    {\n                                        value: 'Education',\n                                        label: 'Education'\n                                    },\n                                    {\n                                        value: 'Retail',\n                                        label: 'Retail'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Regional Settings\",\n                    padding: \"lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FormField, {\n                                label: \"Default Currency\",\n                                name: \"defaultCurrency\",\n                                type: \"select\",\n                                value: generalSettings.defaultCurrency,\n                                onChange: handleGeneralChange,\n                                options: [\n                                    {\n                                        value: 'USD',\n                                        label: 'USD ($)'\n                                    },\n                                    {\n                                        value: 'EUR',\n                                        label: 'EUR (€)'\n                                    },\n                                    {\n                                        value: 'GBP',\n                                        label: 'GBP (£)'\n                                    },\n                                    {\n                                        value: 'CAD',\n                                        label: 'CAD (C$)'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FormField, {\n                                label: \"Date Format\",\n                                name: \"dateFormat\",\n                                type: \"select\",\n                                value: generalSettings.dateFormat,\n                                onChange: handleGeneralChange,\n                                options: [\n                                    {\n                                        value: 'MM/DD/YYYY',\n                                        label: 'MM/DD/YYYY'\n                                    },\n                                    {\n                                        value: 'DD/MM/YYYY',\n                                        label: 'DD/MM/YYYY'\n                                    },\n                                    {\n                                        value: 'YYYY-MM-DD',\n                                        label: 'YYYY-MM-DD'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FormField, {\n                                label: \"Time Format\",\n                                name: \"timeFormat\",\n                                type: \"select\",\n                                value: generalSettings.timeFormat,\n                                onChange: handleGeneralChange,\n                                options: [\n                                    {\n                                        value: '12-hour',\n                                        label: '12-hour (AM/PM)'\n                                    },\n                                    {\n                                        value: '24-hour',\n                                        label: '24-hour'\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n            lineNumber: 53,\n            columnNumber: 5\n        }, this);\n    const renderIntegrations = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Job Board Integrations\",\n                    padding: \"lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            {\n                                name: 'LinkedIn',\n                                key: 'linkedinEnabled',\n                                description: 'Post jobs and source candidates from LinkedIn'\n                            },\n                            {\n                                name: 'Indeed',\n                                key: 'indeedEnabled',\n                                description: 'Automatically post jobs to Indeed'\n                            },\n                            {\n                                name: 'Glassdoor',\n                                key: 'glassdoorEnabled',\n                                description: 'Sync company reviews and job postings'\n                            }\n                        ].map((integration)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-4 border rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                                                    name: \"link\",\n                                                    size: \"sm\",\n                                                    className: \"text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: integration.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: integration.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                variant: integrationSettings[integration.key] ? 'success' : 'default',\n                                                children: integrationSettings[integration.key] ? 'Connected' : 'Disconnected'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: integrationSettings[integration.key] ? 'Configure' : 'Connect'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, integration.key, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Communication Tools\",\n                    padding: \"lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center p-4 border rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                                                name: \"message\",\n                                                size: \"sm\",\n                                                className: \"text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Slack\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Get notifications in your Slack workspace\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                            variant: \"success\",\n                                            children: \"Connected\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"Configure\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 5\n        }, this);\n    const renderContent = ()=>{\n        switch(activeTab){\n            case 'general':\n                return renderGeneralSettings();\n            case 'integrations':\n                return renderIntegrations();\n            case 'notifications':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Notification Preferences\",\n                    padding: \"lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Notification settings would go here\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 11\n                }, this);\n            case 'security':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Security Settings\",\n                    padding: \"lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Security settings would go here\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, this);\n            case 'billing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Billing & Subscription\",\n                    padding: \"lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Billing settings would go here\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Settings\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage your application preferences and integrations\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"primary\",\n                        onClick: handleSaveSettings,\n                        children: \"Save Changes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            padding: \"sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1\",\n                                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors \".concat(activeTab === tab.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                                                name: tab.icon,\n                                                size: \"sm\",\n                                                className: activeTab === tab.id ? 'text-blue-700' : 'text-gray-500'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: renderContent()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"1A89vzZsMOxgQQJchY8ipaex8Ik=\");\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvc2V0dGluZ3MvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUwQjtBQU9KO0FBRVAsU0FBU007O0lBQ3RCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHUixxREFBYyxDQUFDO0lBRWpELE1BQU0sQ0FBQ1UsaUJBQWlCQyxtQkFBbUIsR0FBR1gscURBQWMsQ0FBQztRQUMzRFksYUFBYTtRQUNiQyxnQkFBZ0I7UUFDaEJDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxpQkFBaUI7UUFDakJDLFlBQVk7UUFDWkMsWUFBWTtJQUNkO0lBRUEsTUFBTSxDQUFDQyxxQkFBcUJDLHVCQUF1QixHQUFHcEIscURBQWMsQ0FBQztRQUNuRXFCLGlCQUFpQjtRQUNqQkMsZUFBZTtRQUNmQyxrQkFBa0I7UUFDbEJDLGNBQWM7UUFDZEMsZUFBZTtJQUNqQjtJQUVBLE1BQU1DLE9BQU87UUFDWDtZQUFFQyxJQUFJO1lBQVdDLE9BQU87WUFBV0MsTUFBTTtRQUFXO1FBQ3BEO1lBQUVGLElBQUk7WUFBZ0JDLE9BQU87WUFBZ0JDLE1BQU07UUFBTztRQUMxRDtZQUFFRixJQUFJO1lBQWlCQyxPQUFPO1lBQWlCQyxNQUFNO1FBQU87UUFDNUQ7WUFBRUYsSUFBSTtZQUFZQyxPQUFPO1lBQVlDLE1BQU07UUFBUztRQUNwRDtZQUFFRixJQUFJO1lBQVdDLE9BQU87WUFBV0MsTUFBTTtRQUFjO0tBQ3hEO0lBRUQsTUFBTUMsc0JBQXNCLENBQUNDLE1BQWNDO1FBQ3pDckIsbUJBQW1Cc0IsQ0FBQUEsT0FBUztnQkFDMUIsR0FBR0EsSUFBSTtnQkFDUCxDQUFDRixLQUFLLEVBQUVDO1lBQ1Y7SUFDRjtJQUVBLE1BQU1FLHFCQUFxQjtRQUN6QkMsUUFBUUMsR0FBRyxDQUFDO0lBQ2Q7SUFFQSxNQUFNQyx3QkFBd0Isa0JBQzVCLDhEQUFDQztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ3RDLDZDQUFJQTtvQkFBQ3VDLE9BQU07b0JBQXNCQyxTQUFROzhCQUN4Qyw0RUFBQ0g7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDcEMsa0RBQVNBO2dDQUNSeUIsT0FBTTtnQ0FDTkcsTUFBSztnQ0FDTEMsT0FBT3RCLGdCQUFnQkUsV0FBVztnQ0FDbEM4QixVQUFVWjtnQ0FDVmEsUUFBUTs7Ozs7OzBDQUVWLDhEQUFDeEMsa0RBQVNBO2dDQUNSeUIsT0FBTTtnQ0FDTkcsTUFBSztnQ0FDTGEsTUFBSztnQ0FDTFosT0FBT3RCLGdCQUFnQkcsY0FBYztnQ0FDckM2QixVQUFVWjs7Ozs7OzBDQUVaLDhEQUFDM0Isa0RBQVNBO2dDQUNSeUIsT0FBTTtnQ0FDTkcsTUFBSztnQ0FDTGEsTUFBSztnQ0FDTFosT0FBT3RCLGdCQUFnQkksV0FBVztnQ0FDbEM0QixVQUFVWjtnQ0FDVmUsU0FBUztvQ0FDUDt3Q0FBRWIsT0FBTzt3Q0FBUUosT0FBTztvQ0FBaUI7b0NBQ3pDO3dDQUFFSSxPQUFPO3dDQUFTSixPQUFPO29DQUFrQjtvQ0FDM0M7d0NBQUVJLE9BQU87d0NBQVVKLE9BQU87b0NBQW1CO29DQUM3Qzt3Q0FBRUksT0FBTzt3Q0FBV0osT0FBTztvQ0FBb0I7b0NBQy9DO3dDQUFFSSxPQUFPO3dDQUFRSixPQUFPO29DQUFpQjtpQ0FDMUM7Ozs7OzswQ0FFSCw4REFBQ3pCLGtEQUFTQTtnQ0FDUnlCLE9BQU07Z0NBQ05HLE1BQUs7Z0NBQ0xhLE1BQUs7Z0NBQ0xaLE9BQU90QixnQkFBZ0JLLFFBQVE7Z0NBQy9CMkIsVUFBVVo7Z0NBQ1ZlLFNBQVM7b0NBQ1A7d0NBQUViLE9BQU87d0NBQWNKLE9BQU87b0NBQWE7b0NBQzNDO3dDQUFFSSxPQUFPO3dDQUFjSixPQUFPO29DQUFhO29DQUMzQzt3Q0FBRUksT0FBTzt3Q0FBV0osT0FBTztvQ0FBVTtvQ0FDckM7d0NBQUVJLE9BQU87d0NBQWFKLE9BQU87b0NBQVk7b0NBQ3pDO3dDQUFFSSxPQUFPO3dDQUFVSixPQUFPO29DQUFTO2lDQUNwQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBS1AsOERBQUMzQiw2Q0FBSUE7b0JBQUN1QyxPQUFNO29CQUFvQkMsU0FBUTs4QkFDdEMsNEVBQUNIO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3BDLGtEQUFTQTtnQ0FDUnlCLE9BQU07Z0NBQ05HLE1BQUs7Z0NBQ0xhLE1BQUs7Z0NBQ0xaLE9BQU90QixnQkFBZ0JNLGVBQWU7Z0NBQ3RDMEIsVUFBVVo7Z0NBQ1ZlLFNBQVM7b0NBQ1A7d0NBQUViLE9BQU87d0NBQU9KLE9BQU87b0NBQVU7b0NBQ2pDO3dDQUFFSSxPQUFPO3dDQUFPSixPQUFPO29DQUFVO29DQUNqQzt3Q0FBRUksT0FBTzt3Q0FBT0osT0FBTztvQ0FBVTtvQ0FDakM7d0NBQUVJLE9BQU87d0NBQU9KLE9BQU87b0NBQVc7aUNBQ25DOzs7Ozs7MENBRUgsOERBQUN6QixrREFBU0E7Z0NBQ1J5QixPQUFNO2dDQUNORyxNQUFLO2dDQUNMYSxNQUFLO2dDQUNMWixPQUFPdEIsZ0JBQWdCTyxVQUFVO2dDQUNqQ3lCLFVBQVVaO2dDQUNWZSxTQUFTO29DQUNQO3dDQUFFYixPQUFPO3dDQUFjSixPQUFPO29DQUFhO29DQUMzQzt3Q0FBRUksT0FBTzt3Q0FBY0osT0FBTztvQ0FBYTtvQ0FDM0M7d0NBQUVJLE9BQU87d0NBQWNKLE9BQU87b0NBQWE7aUNBQzVDOzs7Ozs7MENBRUgsOERBQUN6QixrREFBU0E7Z0NBQ1J5QixPQUFNO2dDQUNORyxNQUFLO2dDQUNMYSxNQUFLO2dDQUNMWixPQUFPdEIsZ0JBQWdCUSxVQUFVO2dDQUNqQ3dCLFVBQVVaO2dDQUNWZSxTQUFTO29DQUNQO3dDQUFFYixPQUFPO3dDQUFXSixPQUFPO29DQUFrQjtvQ0FDN0M7d0NBQUVJLE9BQU87d0NBQVdKLE9BQU87b0NBQVU7aUNBQ3RDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU9YLE1BQU1rQixxQkFBcUIsa0JBQ3pCLDhEQUFDUjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ3RDLDZDQUFJQTtvQkFBQ3VDLE9BQU07b0JBQXlCQyxTQUFROzhCQUMzQyw0RUFBQ0g7d0JBQUlDLFdBQVU7a0NBQ1o7NEJBQ0M7Z0NBQUVSLE1BQU07Z0NBQVlnQixLQUFLO2dDQUFtQkMsYUFBYTs0QkFBZ0Q7NEJBQ3pHO2dDQUFFakIsTUFBTTtnQ0FBVWdCLEtBQUs7Z0NBQWlCQyxhQUFhOzRCQUFvQzs0QkFDekY7Z0NBQUVqQixNQUFNO2dDQUFhZ0IsS0FBSztnQ0FBb0JDLGFBQWE7NEJBQXdDO3lCQUNwRyxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsNEJBQ0wsOERBQUNaO2dDQUEwQkMsV0FBVTs7a0RBQ25DLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDbEMsNkNBQUlBO29EQUFDMEIsTUFBSztvREFBT29CLE1BQUs7b0RBQUtaLFdBQVU7Ozs7Ozs7Ozs7OzBEQUV4Qyw4REFBQ0Q7O2tFQUNDLDhEQUFDYzt3REFBRWIsV0FBVTtrRUFBNkJXLFlBQVluQixJQUFJOzs7Ozs7a0VBQzFELDhEQUFDcUI7d0RBQUViLFdBQVU7a0VBQXlCVyxZQUFZRixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR2pFLDhEQUFDVjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNuQyw4Q0FBS0E7Z0RBQUNpRCxTQUFTbEMsbUJBQW1CLENBQUMrQixZQUFZSCxHQUFHLENBQXFDLEdBQUcsWUFBWTswREFDcEc1QixtQkFBbUIsQ0FBQytCLFlBQVlILEdBQUcsQ0FBcUMsR0FBRyxjQUFjOzs7Ozs7MERBRTVGLDhEQUFDN0MsK0NBQU1BO2dEQUFDbUQsU0FBUTtnREFBVUYsTUFBSzswREFDNUJoQyxtQkFBbUIsQ0FBQytCLFlBQVlILEdBQUcsQ0FBcUMsR0FBRyxjQUFjOzs7Ozs7Ozs7Ozs7OytCQWZ0RkcsWUFBWUgsR0FBRzs7Ozs7Ozs7Ozs7Ozs7OzhCQXVCL0IsOERBQUM5Qyw2Q0FBSUE7b0JBQUN1QyxPQUFNO29CQUFzQkMsU0FBUTs4QkFDeEMsNEVBQUNIO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNsQyw2Q0FBSUE7Z0RBQUMwQixNQUFLO2dEQUFVb0IsTUFBSztnREFBS1osV0FBVTs7Ozs7Ozs7Ozs7c0RBRTNDLDhEQUFDRDs7OERBQ0MsOERBQUNjO29EQUFFYixXQUFVOzhEQUE0Qjs7Ozs7OzhEQUN6Qyw4REFBQ2E7b0RBQUViLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR3pDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNuQyw4Q0FBS0E7NENBQUNpRCxTQUFRO3NEQUFVOzs7Ozs7c0RBQ3pCLDhEQUFDbkQsK0NBQU1BOzRDQUFDbUQsU0FBUTs0Q0FBVUYsTUFBSztzREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVFoRCxNQUFNRyxnQkFBZ0I7UUFDcEIsT0FBUS9DO1lBQ04sS0FBSztnQkFDSCxPQUFPOEI7WUFDVCxLQUFLO2dCQUNILE9BQU9TO1lBQ1QsS0FBSztnQkFDSCxxQkFDRSw4REFBQzdDLDZDQUFJQTtvQkFBQ3VDLE9BQU07b0JBQTJCQyxTQUFROzhCQUM3Qyw0RUFBQ1c7d0JBQUViLFdBQVU7a0NBQWdCOzs7Ozs7Ozs7OztZQUduQyxLQUFLO2dCQUNILHFCQUNFLDhEQUFDdEMsNkNBQUlBO29CQUFDdUMsT0FBTTtvQkFBb0JDLFNBQVE7OEJBQ3RDLDRFQUFDVzt3QkFBRWIsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7O1lBR25DLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUN0Qyw2Q0FBSUE7b0JBQUN1QyxPQUFNO29CQUF5QkMsU0FBUTs4QkFDM0MsNEVBQUNXO3dCQUFFYixXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7WUFHbkM7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7OzBDQUNDLDhEQUFDaUI7Z0NBQUdoQixXQUFVOzBDQUFtQzs7Ozs7OzBDQUNqRCw4REFBQ2E7Z0NBQUViLFdBQVU7MENBQWdCOzs7Ozs7Ozs7Ozs7a0NBRS9CLDhEQUFDckMsK0NBQU1BO3dCQUFDbUQsU0FBUTt3QkFBVUcsU0FBU3RCO2tDQUFvQjs7Ozs7Ozs7Ozs7OzBCQUt6RCw4REFBQ0k7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ3RDLDZDQUFJQTs0QkFBQ3dDLFNBQVE7c0NBQ1osNEVBQUNnQjtnQ0FBSWxCLFdBQVU7MENBQ1piLEtBQUt1QixHQUFHLENBQUMsQ0FBQ1Msb0JBQ1QsOERBQUNDO3dDQUVDSCxTQUFTLElBQU1oRCxhQUFha0QsSUFBSS9CLEVBQUU7d0NBQ2xDWSxXQUFXLGlHQUlWLE9BSENoQyxjQUFjbUQsSUFBSS9CLEVBQUUsR0FDaEIsNkJBQ0E7OzBEQUdOLDhEQUFDdEIsNkNBQUlBO2dEQUNIMEIsTUFBTTJCLElBQUk3QixJQUFJO2dEQUNkc0IsTUFBSztnREFDTFosV0FBV2hDLGNBQWNtRCxJQUFJL0IsRUFBRSxHQUFHLGtCQUFrQjs7Ozs7OzBEQUV0RCw4REFBQ2lDOzBEQUFNRixJQUFJOUIsS0FBSzs7Ozs7Ozt1Q0FiWDhCLElBQUkvQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FxQnJCLDhEQUFDVzt3QkFBSUMsV0FBVTtrQ0FDWmU7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtYO0dBdFF3QmhEO0tBQUFBIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXFdlYiBGaWxlc1xcbmVhcnRla3BvZFxcYWktcmVrcnVpdC1wcm9cXGFpLXJla3J1aXQtcHJvIG5leHRcXGFpLXJla3J1aXQtcHJvXFxzcmNcXGFwcFxcKGRhc2hib2FyZClcXHNldHRpbmdzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBcbiAgQ2FyZCwgXG4gIEJ1dHRvbixcbiAgRm9ybUZpZWxkLFxuICBCYWRnZSxcbiAgSWNvblxufSBmcm9tICdAL2NvbXBvbmVudHMnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXR0aW5nc1BhZ2UoKSB7XG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSBSZWFjdC51c2VTdGF0ZSgnZ2VuZXJhbCcpO1xuICBcbiAgY29uc3QgW2dlbmVyYWxTZXR0aW5ncywgc2V0R2VuZXJhbFNldHRpbmdzXSA9IFJlYWN0LnVzZVN0YXRlKHtcbiAgICBjb21wYW55TmFtZTogJ1RlY2hDb3JwIEluYy4nLFxuICAgIGNvbXBhbnlXZWJzaXRlOiAnaHR0cHM6Ly90ZWNoY29ycC5jb20nLFxuICAgIGNvbXBhbnlTaXplOiAnMTAwLTUwMCcsXG4gICAgaW5kdXN0cnk6ICdUZWNobm9sb2d5JyxcbiAgICBkZWZhdWx0Q3VycmVuY3k6ICdVU0QnLFxuICAgIGRhdGVGb3JtYXQ6ICdNTS9ERC9ZWVlZJyxcbiAgICB0aW1lRm9ybWF0OiAnMTItaG91cidcbiAgfSk7XG5cbiAgY29uc3QgW2ludGVncmF0aW9uU2V0dGluZ3MsIHNldEludGVncmF0aW9uU2V0dGluZ3NdID0gUmVhY3QudXNlU3RhdGUoe1xuICAgIGxpbmtlZGluRW5hYmxlZDogdHJ1ZSxcbiAgICBpbmRlZWRFbmFibGVkOiB0cnVlLFxuICAgIGdsYXNzZG9vckVuYWJsZWQ6IGZhbHNlLFxuICAgIHNsYWNrRW5hYmxlZDogdHJ1ZSxcbiAgICBlbWFpbFByb3ZpZGVyOiAnZ21haWwnXG4gIH0pO1xuXG4gIGNvbnN0IHRhYnMgPSBbXG4gICAgeyBpZDogJ2dlbmVyYWwnLCBsYWJlbDogJ0dlbmVyYWwnLCBpY29uOiAnc2V0dGluZ3MnIH0sXG4gICAgeyBpZDogJ2ludGVncmF0aW9ucycsIGxhYmVsOiAnSW50ZWdyYXRpb25zJywgaWNvbjogJ2xpbmsnIH0sXG4gICAgeyBpZDogJ25vdGlmaWNhdGlvbnMnLCBsYWJlbDogJ05vdGlmaWNhdGlvbnMnLCBpY29uOiAnYmVsbCcgfSxcbiAgICB7IGlkOiAnc2VjdXJpdHknLCBsYWJlbDogJ1NlY3VyaXR5JywgaWNvbjogJ3NoaWVsZCcgfSxcbiAgICB7IGlkOiAnYmlsbGluZycsIGxhYmVsOiAnQmlsbGluZycsIGljb246ICdjcmVkaXQtY2FyZCcgfVxuICBdO1xuXG4gIGNvbnN0IGhhbmRsZUdlbmVyYWxDaGFuZ2UgPSAobmFtZTogc3RyaW5nLCB2YWx1ZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0R2VuZXJhbFNldHRpbmdzKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbbmFtZV06IHZhbHVlXG4gICAgfSkpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNhdmVTZXR0aW5ncyA9ICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnU2F2ZSBzZXR0aW5ncycpO1xuICB9O1xuXG4gIGNvbnN0IHJlbmRlckdlbmVyYWxTZXR0aW5ncyA9ICgpID0+IChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgPENhcmQgdGl0bGU9XCJDb21wYW55IEluZm9ybWF0aW9uXCIgcGFkZGluZz1cImxnXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgIDxGb3JtRmllbGRcbiAgICAgICAgICAgIGxhYmVsPVwiQ29tcGFueSBOYW1lXCJcbiAgICAgICAgICAgIG5hbWU9XCJjb21wYW55TmFtZVwiXG4gICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbFNldHRpbmdzLmNvbXBhbnlOYW1lfVxuICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUdlbmVyYWxDaGFuZ2V9XG4gICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgIC8+XG4gICAgICAgICAgPEZvcm1GaWVsZFxuICAgICAgICAgICAgbGFiZWw9XCJXZWJzaXRlXCJcbiAgICAgICAgICAgIG5hbWU9XCJjb21wYW55V2Vic2l0ZVwiXG4gICAgICAgICAgICB0eXBlPVwidXJsXCJcbiAgICAgICAgICAgIHZhbHVlPXtnZW5lcmFsU2V0dGluZ3MuY29tcGFueVdlYnNpdGV9XG4gICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlR2VuZXJhbENoYW5nZX1cbiAgICAgICAgICAvPlxuICAgICAgICAgIDxGb3JtRmllbGRcbiAgICAgICAgICAgIGxhYmVsPVwiQ29tcGFueSBTaXplXCJcbiAgICAgICAgICAgIG5hbWU9XCJjb21wYW55U2l6ZVwiXG4gICAgICAgICAgICB0eXBlPVwic2VsZWN0XCJcbiAgICAgICAgICAgIHZhbHVlPXtnZW5lcmFsU2V0dGluZ3MuY29tcGFueVNpemV9XG4gICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlR2VuZXJhbENoYW5nZX1cbiAgICAgICAgICAgIG9wdGlvbnM9e1tcbiAgICAgICAgICAgICAgeyB2YWx1ZTogJzEtMTAnLCBsYWJlbDogJzEtMTAgZW1wbG95ZWVzJyB9LFxuICAgICAgICAgICAgICB7IHZhbHVlOiAnMTEtNTAnLCBsYWJlbDogJzExLTUwIGVtcGxveWVlcycgfSxcbiAgICAgICAgICAgICAgeyB2YWx1ZTogJzUxLTEwMCcsIGxhYmVsOiAnNTEtMTAwIGVtcGxveWVlcycgfSxcbiAgICAgICAgICAgICAgeyB2YWx1ZTogJzEwMC01MDAnLCBsYWJlbDogJzEwMC01MDAgZW1wbG95ZWVzJyB9LFxuICAgICAgICAgICAgICB7IHZhbHVlOiAnNTAwKycsIGxhYmVsOiAnNTAwKyBlbXBsb3llZXMnIH1cbiAgICAgICAgICAgIF19XG4gICAgICAgICAgLz5cbiAgICAgICAgICA8Rm9ybUZpZWxkXG4gICAgICAgICAgICBsYWJlbD1cIkluZHVzdHJ5XCJcbiAgICAgICAgICAgIG5hbWU9XCJpbmR1c3RyeVwiXG4gICAgICAgICAgICB0eXBlPVwic2VsZWN0XCJcbiAgICAgICAgICAgIHZhbHVlPXtnZW5lcmFsU2V0dGluZ3MuaW5kdXN0cnl9XG4gICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlR2VuZXJhbENoYW5nZX1cbiAgICAgICAgICAgIG9wdGlvbnM9e1tcbiAgICAgICAgICAgICAgeyB2YWx1ZTogJ1RlY2hub2xvZ3knLCBsYWJlbDogJ1RlY2hub2xvZ3knIH0sXG4gICAgICAgICAgICAgIHsgdmFsdWU6ICdIZWFsdGhjYXJlJywgbGFiZWw6ICdIZWFsdGhjYXJlJyB9LFxuICAgICAgICAgICAgICB7IHZhbHVlOiAnRmluYW5jZScsIGxhYmVsOiAnRmluYW5jZScgfSxcbiAgICAgICAgICAgICAgeyB2YWx1ZTogJ0VkdWNhdGlvbicsIGxhYmVsOiAnRWR1Y2F0aW9uJyB9LFxuICAgICAgICAgICAgICB7IHZhbHVlOiAnUmV0YWlsJywgbGFiZWw6ICdSZXRhaWwnIH1cbiAgICAgICAgICAgIF19XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIDxDYXJkIHRpdGxlPVwiUmVnaW9uYWwgU2V0dGluZ3NcIiBwYWRkaW5nPVwibGdcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgPEZvcm1GaWVsZFxuICAgICAgICAgICAgbGFiZWw9XCJEZWZhdWx0IEN1cnJlbmN5XCJcbiAgICAgICAgICAgIG5hbWU9XCJkZWZhdWx0Q3VycmVuY3lcIlxuICAgICAgICAgICAgdHlwZT1cInNlbGVjdFwiXG4gICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbFNldHRpbmdzLmRlZmF1bHRDdXJyZW5jeX1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVHZW5lcmFsQ2hhbmdlfVxuICAgICAgICAgICAgb3B0aW9ucz17W1xuICAgICAgICAgICAgICB7IHZhbHVlOiAnVVNEJywgbGFiZWw6ICdVU0QgKCQpJyB9LFxuICAgICAgICAgICAgICB7IHZhbHVlOiAnRVVSJywgbGFiZWw6ICdFVVIgKOKCrCknIH0sXG4gICAgICAgICAgICAgIHsgdmFsdWU6ICdHQlAnLCBsYWJlbDogJ0dCUCAowqMpJyB9LFxuICAgICAgICAgICAgICB7IHZhbHVlOiAnQ0FEJywgbGFiZWw6ICdDQUQgKEMkKScgfVxuICAgICAgICAgICAgXX1cbiAgICAgICAgICAvPlxuICAgICAgICAgIDxGb3JtRmllbGRcbiAgICAgICAgICAgIGxhYmVsPVwiRGF0ZSBGb3JtYXRcIlxuICAgICAgICAgICAgbmFtZT1cImRhdGVGb3JtYXRcIlxuICAgICAgICAgICAgdHlwZT1cInNlbGVjdFwiXG4gICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbFNldHRpbmdzLmRhdGVGb3JtYXR9XG4gICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlR2VuZXJhbENoYW5nZX1cbiAgICAgICAgICAgIG9wdGlvbnM9e1tcbiAgICAgICAgICAgICAgeyB2YWx1ZTogJ01NL0REL1lZWVknLCBsYWJlbDogJ01NL0REL1lZWVknIH0sXG4gICAgICAgICAgICAgIHsgdmFsdWU6ICdERC9NTS9ZWVlZJywgbGFiZWw6ICdERC9NTS9ZWVlZJyB9LFxuICAgICAgICAgICAgICB7IHZhbHVlOiAnWVlZWS1NTS1ERCcsIGxhYmVsOiAnWVlZWS1NTS1ERCcgfVxuICAgICAgICAgICAgXX1cbiAgICAgICAgICAvPlxuICAgICAgICAgIDxGb3JtRmllbGRcbiAgICAgICAgICAgIGxhYmVsPVwiVGltZSBGb3JtYXRcIlxuICAgICAgICAgICAgbmFtZT1cInRpbWVGb3JtYXRcIlxuICAgICAgICAgICAgdHlwZT1cInNlbGVjdFwiXG4gICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbFNldHRpbmdzLnRpbWVGb3JtYXR9XG4gICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlR2VuZXJhbENoYW5nZX1cbiAgICAgICAgICAgIG9wdGlvbnM9e1tcbiAgICAgICAgICAgICAgeyB2YWx1ZTogJzEyLWhvdXInLCBsYWJlbDogJzEyLWhvdXIgKEFNL1BNKScgfSxcbiAgICAgICAgICAgICAgeyB2YWx1ZTogJzI0LWhvdXInLCBsYWJlbDogJzI0LWhvdXInIH1cbiAgICAgICAgICAgIF19XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmQ+XG4gICAgPC9kaXY+XG4gICk7XG5cbiAgY29uc3QgcmVuZGVySW50ZWdyYXRpb25zID0gKCkgPT4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICA8Q2FyZCB0aXRsZT1cIkpvYiBCb2FyZCBJbnRlZ3JhdGlvbnNcIiBwYWRkaW5nPVwibGdcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICB7W1xuICAgICAgICAgICAgeyBuYW1lOiAnTGlua2VkSW4nLCBrZXk6ICdsaW5rZWRpbkVuYWJsZWQnLCBkZXNjcmlwdGlvbjogJ1Bvc3Qgam9icyBhbmQgc291cmNlIGNhbmRpZGF0ZXMgZnJvbSBMaW5rZWRJbicgfSxcbiAgICAgICAgICAgIHsgbmFtZTogJ0luZGVlZCcsIGtleTogJ2luZGVlZEVuYWJsZWQnLCBkZXNjcmlwdGlvbjogJ0F1dG9tYXRpY2FsbHkgcG9zdCBqb2JzIHRvIEluZGVlZCcgfSxcbiAgICAgICAgICAgIHsgbmFtZTogJ0dsYXNzZG9vcicsIGtleTogJ2dsYXNzZG9vckVuYWJsZWQnLCBkZXNjcmlwdGlvbjogJ1N5bmMgY29tcGFueSByZXZpZXdzIGFuZCBqb2IgcG9zdGluZ3MnIH1cbiAgICAgICAgICBdLm1hcCgoaW50ZWdyYXRpb24pID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtpbnRlZ3JhdGlvbi5rZXl9IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBwLTQgYm9yZGVyIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ibHVlLTEwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8SWNvbiBuYW1lPVwibGlua1wiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e2ludGVncmF0aW9uLm5hbWV9PC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e2ludGVncmF0aW9uLmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9e2ludGVncmF0aW9uU2V0dGluZ3NbaW50ZWdyYXRpb24ua2V5IGFzIGtleW9mIHR5cGVvZiBpbnRlZ3JhdGlvblNldHRpbmdzXSA/ICdzdWNjZXNzJyA6ICdkZWZhdWx0J30+XG4gICAgICAgICAgICAgICAgICB7aW50ZWdyYXRpb25TZXR0aW5nc1tpbnRlZ3JhdGlvbi5rZXkgYXMga2V5b2YgdHlwZW9mIGludGVncmF0aW9uU2V0dGluZ3NdID8gJ0Nvbm5lY3RlZCcgOiAnRGlzY29ubmVjdGVkJ31cbiAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBzaXplPVwic21cIj5cbiAgICAgICAgICAgICAgICAgIHtpbnRlZ3JhdGlvblNldHRpbmdzW2ludGVncmF0aW9uLmtleSBhcyBrZXlvZiB0eXBlb2YgaW50ZWdyYXRpb25TZXR0aW5nc10gPyAnQ29uZmlndXJlJyA6ICdDb25uZWN0J31cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIDxDYXJkIHRpdGxlPVwiQ29tbXVuaWNhdGlvbiBUb29sc1wiIHBhZGRpbmc9XCJsZ1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHAtNCBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctcHVycGxlLTEwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPEljb24gbmFtZT1cIm1lc3NhZ2VcIiBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ0ZXh0LXB1cnBsZS02MDBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+U2xhY2s8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+R2V0IG5vdGlmaWNhdGlvbnMgaW4geW91ciBTbGFjayB3b3Jrc3BhY2U8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cInN1Y2Nlc3NcIj5Db25uZWN0ZWQ8L0JhZGdlPlxuICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCI+Q29uZmlndXJlPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmQ+XG4gICAgPC9kaXY+XG4gICk7XG5cbiAgY29uc3QgcmVuZGVyQ29udGVudCA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKGFjdGl2ZVRhYikge1xuICAgICAgY2FzZSAnZ2VuZXJhbCc6XG4gICAgICAgIHJldHVybiByZW5kZXJHZW5lcmFsU2V0dGluZ3MoKTtcbiAgICAgIGNhc2UgJ2ludGVncmF0aW9ucyc6XG4gICAgICAgIHJldHVybiByZW5kZXJJbnRlZ3JhdGlvbnMoKTtcbiAgICAgIGNhc2UgJ25vdGlmaWNhdGlvbnMnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxDYXJkIHRpdGxlPVwiTm90aWZpY2F0aW9uIFByZWZlcmVuY2VzXCIgcGFkZGluZz1cImxnXCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+Tm90aWZpY2F0aW9uIHNldHRpbmdzIHdvdWxkIGdvIGhlcmU8L3A+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICApO1xuICAgICAgY2FzZSAnc2VjdXJpdHknOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxDYXJkIHRpdGxlPVwiU2VjdXJpdHkgU2V0dGluZ3NcIiBwYWRkaW5nPVwibGdcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5TZWN1cml0eSBzZXR0aW5ncyB3b3VsZCBnbyBoZXJlPC9wPlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ2JpbGxpbmcnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxDYXJkIHRpdGxlPVwiQmlsbGluZyAmIFN1YnNjcmlwdGlvblwiIHBhZGRpbmc9XCJsZ1wiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkJpbGxpbmcgc2V0dGluZ3Mgd291bGQgZ28gaGVyZTwvcD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICk7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+U2V0dGluZ3M8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5NYW5hZ2UgeW91ciBhcHBsaWNhdGlvbiBwcmVmZXJlbmNlcyBhbmQgaW50ZWdyYXRpb25zPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwicHJpbWFyeVwiIG9uQ2xpY2s9e2hhbmRsZVNhdmVTZXR0aW5nc30+XG4gICAgICAgICAgU2F2ZSBDaGFuZ2VzXG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICB7LyogU2V0dGluZ3MgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0xXCI+XG4gICAgICAgICAgPENhcmQgcGFkZGluZz1cInNtXCI+XG4gICAgICAgICAgICA8bmF2IGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICB7dGFicy5tYXAoKHRhYikgPT4gKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGtleT17dGFiLmlkfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKHRhYi5pZCl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSB0YWIuaWRcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTUwIHRleHQtYmx1ZS03MDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxJY29uIFxuICAgICAgICAgICAgICAgICAgICBuYW1lPXt0YWIuaWNvbn0gXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiIFxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2FjdGl2ZVRhYiA9PT0gdGFiLmlkID8gJ3RleHQtYmx1ZS03MDAnIDogJ3RleHQtZ3JheS01MDAnfSBcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj57dGFiLmxhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L25hdj5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTZXR0aW5ncyBDb250ZW50ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTNcIj5cbiAgICAgICAgICB7cmVuZGVyQ29udGVudCgpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ2FyZCIsIkJ1dHRvbiIsIkZvcm1GaWVsZCIsIkJhZGdlIiwiSWNvbiIsIlNldHRpbmdzUGFnZSIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInVzZVN0YXRlIiwiZ2VuZXJhbFNldHRpbmdzIiwic2V0R2VuZXJhbFNldHRpbmdzIiwiY29tcGFueU5hbWUiLCJjb21wYW55V2Vic2l0ZSIsImNvbXBhbnlTaXplIiwiaW5kdXN0cnkiLCJkZWZhdWx0Q3VycmVuY3kiLCJkYXRlRm9ybWF0IiwidGltZUZvcm1hdCIsImludGVncmF0aW9uU2V0dGluZ3MiLCJzZXRJbnRlZ3JhdGlvblNldHRpbmdzIiwibGlua2VkaW5FbmFibGVkIiwiaW5kZWVkRW5hYmxlZCIsImdsYXNzZG9vckVuYWJsZWQiLCJzbGFja0VuYWJsZWQiLCJlbWFpbFByb3ZpZGVyIiwidGFicyIsImlkIiwibGFiZWwiLCJpY29uIiwiaGFuZGxlR2VuZXJhbENoYW5nZSIsIm5hbWUiLCJ2YWx1ZSIsInByZXYiLCJoYW5kbGVTYXZlU2V0dGluZ3MiLCJjb25zb2xlIiwibG9nIiwicmVuZGVyR2VuZXJhbFNldHRpbmdzIiwiZGl2IiwiY2xhc3NOYW1lIiwidGl0bGUiLCJwYWRkaW5nIiwib25DaGFuZ2UiLCJyZXF1aXJlZCIsInR5cGUiLCJvcHRpb25zIiwicmVuZGVySW50ZWdyYXRpb25zIiwia2V5IiwiZGVzY3JpcHRpb24iLCJtYXAiLCJpbnRlZ3JhdGlvbiIsInNpemUiLCJwIiwidmFyaWFudCIsInJlbmRlckNvbnRlbnQiLCJoMSIsIm9uQ2xpY2siLCJuYXYiLCJ0YWIiLCJidXR0b24iLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx\n"));

/***/ })

});