import React from 'react';
import { cn } from '@/lib/utils';
import { Card, Icon } from '@/components/ui';
import { QuickAction as QuickActionType } from '@/types';

interface QuickActionProps {
  action: QuickActionType;
  className?: string;
}

const QuickAction: React.FC<QuickActionProps> = ({ action, className }) => {
  const getColorClasses = () => {
    const colors = {
      blue: 'bg-blue-50 hover:bg-blue-100 border-blue-200',
      green: 'bg-green-50 hover:bg-green-100 border-green-200',
      orange: 'bg-orange-50 hover:bg-orange-100 border-orange-200',
      purple: 'bg-purple-50 hover:bg-purple-100 border-purple-200',
    };
    return action.color ? colors[action.color] : colors.blue;
  };

  const getIconColor = () => {
    const colors = {
      blue: 'text-blue-600',
      green: 'text-green-600',
      orange: 'text-orange-600',
      purple: 'text-purple-600',
    };
    return action.color ? colors[action.color] : colors.blue;
  };

  return (
    <Card
      className={cn(
        'cursor-pointer transition-all duration-200 hover:shadow-md border-2',
        getColorClasses(),
        className
      )}
      padding="lg"
      shadow="none"
      onClick={action.action}
    >
      <div className="text-center">
        <div className={cn('inline-flex p-3 rounded-full mb-4', getIconColor())}>
          <Icon name={action.icon} size="xl" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {action.title}
        </h3>
        <p className="text-sm text-gray-600">
          {action.description}
        </p>
      </div>
    </Card>
  );
};

export default QuickAction;
