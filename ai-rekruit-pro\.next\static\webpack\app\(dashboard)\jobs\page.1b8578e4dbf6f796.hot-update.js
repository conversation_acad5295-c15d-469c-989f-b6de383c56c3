"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/jobs/page",{

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirst: () => (/* binding */ capitalizeFirst),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateConsistent: () => (/* binding */ formatDateConsistent),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n// Utility function to merge Tailwind classes\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Format date utilities\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    }).format(date);\n}\n// Consistent date formatting for hydration safety\nfunction formatDateConsistent(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return \"\".concat(month, \"/\").concat(day, \"/\").concat(year);\n}\nfunction formatRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n    if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n    if (diffInSeconds < 2592000) return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n    return formatDate(date);\n}\n// Number formatting utilities\nfunction formatNumber(num) {\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n}\nfunction formatCurrency(amount) {\n    let currency = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'USD';\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: currency\n    }).format(amount);\n}\n// String utilities\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\nfunction capitalizeFirst(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\n// Status color mapping\nfunction getStatusColor(status) {\n    const statusColors = {\n        active: 'text-green-600 bg-green-50',\n        paused: 'text-yellow-600 bg-yellow-50',\n        closed: 'text-red-600 bg-red-50',\n        draft: 'text-gray-600 bg-gray-50',\n        new: 'text-blue-600 bg-blue-50',\n        screening: 'text-purple-600 bg-purple-50',\n        interview: 'text-orange-600 bg-orange-50',\n        offer: 'text-green-600 bg-green-50',\n        hired: 'text-green-700 bg-green-100',\n        rejected: 'text-red-600 bg-red-50'\n    };\n    return statusColors[status] || 'text-gray-600 bg-gray-50';\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});