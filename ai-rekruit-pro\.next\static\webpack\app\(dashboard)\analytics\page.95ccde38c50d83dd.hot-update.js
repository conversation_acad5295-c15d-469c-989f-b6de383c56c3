"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/analytics/page",{

/***/ "(app-pages-browser)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Sidebar = (param)=>{\n    let { items, className } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const handleItemClick = (item)=>{\n        router.push(item.href);\n    };\n    const isActive = (href)=>{\n        // Handle exact match for dashboard\n        if (href === '/dashboard' && pathname === '/dashboard') {\n            return true;\n        }\n        // Handle other routes\n        if (href !== '/dashboard' && pathname.startsWith(href)) {\n            return true;\n        }\n        return false;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('w-64 bg-white border-r border-gray-200 h-full', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-sm\",\n                                children: \"RP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"RecruitPro AI\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-2\",\n                    children: items.map((item)=>{\n                        const active = isActive(item.href);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleItemClick(item),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors', active ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-100'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                                                name: item.icon,\n                                                size: \"sm\",\n                                                className: active ? 'text-blue-700' : 'text-gray-500'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded-full\",\n                                        children: item.badge\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.id, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 text-sm font-medium\",\n                                children: \"JD\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-900 truncate\",\n                                    children: \"John Doe\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 truncate\",\n                                    children: \"HR Manager\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                                name: \"settings\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"gA9e4WsoP6a20xDgQgrFkfMP8lc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Sidebar.tsx\n"));

/***/ })

});