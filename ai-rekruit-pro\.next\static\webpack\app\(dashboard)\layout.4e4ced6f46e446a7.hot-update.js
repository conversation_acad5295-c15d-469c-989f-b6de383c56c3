"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/components/layout/Layout.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Layout.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Layout = (param)=>{\n    let { children, title, showSearch = true, onSearch, headerActions, className } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleSidebar = ()=>{\n        setIsSidebarOpen(!isSidebarOpen);\n    };\n    const closeSidebar = ()=>{\n        setIsSidebarOpen(false);\n    };\n    // Default sidebar navigation items\n    const sidebarItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: 'dashboard',\n            href: '/dashboard',\n            description: 'Overview and key metrics'\n        },\n        {\n            id: 'jobs',\n            label: 'Jobs',\n            icon: 'jobs',\n            href: '/jobs',\n            badge: '5',\n            description: 'Manage job postings'\n        },\n        {\n            id: 'candidates',\n            label: 'Candidates',\n            icon: 'candidates',\n            href: '/candidates',\n            description: 'View and manage candidates'\n        },\n        {\n            id: 'resume-analysis',\n            label: 'Resume Analysis',\n            icon: 'analytics',\n            href: '/resume-analysis',\n            description: 'AI-powered resume analysis'\n        },\n        {\n            id: 'analytics',\n            label: 'Analytics',\n            icon: 'analytics',\n            href: '/analytics',\n            description: 'Recruitment analytics and reports'\n        },\n        {\n            id: 'calendar',\n            label: 'Calendar',\n            icon: 'calendar',\n            href: '/calendar',\n            description: 'Schedule and manage interviews'\n        },\n        {\n            id: 'profile',\n            label: 'Profile',\n            icon: 'settings',\n            href: '/profile',\n            description: 'Your profile settings'\n        },\n        {\n            id: 'settings',\n            label: 'Settings',\n            icon: 'settings',\n            href: '/settings',\n            description: 'Application settings'\n        }\n    ];\n    // Get page title based on current route\n    const getPageTitle = ()=>{\n        if (title) return title;\n        const currentItem = sidebarItems.find((item)=>{\n            if (item.href === '/dashboard' && pathname === '/dashboard') {\n                return true;\n            }\n            if (item.href !== '/dashboard' && pathname.startsWith(item.href)) {\n                return true;\n            }\n            return false;\n        });\n        return (currentItem === null || currentItem === void 0 ? void 0 : currentItem.label) || 'AI RecruitPro';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            isSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: closeSidebar\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\", isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    items: sidebarItems,\n                    onItemClick: closeSidebar\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden lg:ml-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: getPageTitle(),\n                        showSearch: showSearch,\n                        onSearch: onSearch,\n                        actions: headerActions,\n                        onMenuClick: toggleSidebar,\n                        showMenuButton: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex-1 overflow-auto p-4 sm:p-6', className),\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"p6ZuCIDIAQeeV72fxRZ28CwS/Fo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = Layout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Layout.tsx\n"));

/***/ })

});