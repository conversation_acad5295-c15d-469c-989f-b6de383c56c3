"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/profile/page",{

/***/ "(app-pages-browser)/./src/components/layout/Layout.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Layout.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Layout = (param)=>{\n    let { children, title, showSearch = true, onSearch, headerActions, className } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleSidebar = ()=>{\n        setIsSidebarOpen(!isSidebarOpen);\n    };\n    const closeSidebar = ()=>{\n        setIsSidebarOpen(false);\n    };\n    // Default sidebar navigation items\n    const sidebarItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: 'dashboard',\n            href: '/dashboard',\n            description: 'Overview and key metrics'\n        },\n        {\n            id: 'jobs',\n            label: 'Jobs',\n            icon: 'jobs',\n            href: '/jobs',\n            badge: '5',\n            description: 'Manage job postings'\n        },\n        {\n            id: 'candidates',\n            label: 'Candidates',\n            icon: 'candidates',\n            href: '/candidates',\n            description: 'View and manage candidates'\n        },\n        {\n            id: 'resume-analysis',\n            label: 'Resume Analysis',\n            icon: 'analytics',\n            href: '/resume-analysis',\n            description: 'AI-powered resume analysis'\n        },\n        {\n            id: 'analytics',\n            label: 'Analytics',\n            icon: 'analytics',\n            href: '/analytics',\n            description: 'Recruitment analytics and reports'\n        },\n        {\n            id: 'calendar',\n            label: 'Calendar',\n            icon: 'calendar',\n            href: '/calendar',\n            description: 'Schedule and manage interviews'\n        },\n        {\n            id: 'profile',\n            label: 'Profile',\n            icon: 'settings',\n            href: '/profile',\n            description: 'Your profile settings'\n        },\n        {\n            id: 'settings',\n            label: 'Settings',\n            icon: 'settings',\n            href: '/settings',\n            description: 'Application settings'\n        }\n    ];\n    // Get page title based on current route\n    const getPageTitle = ()=>{\n        if (title) return title;\n        const currentItem = sidebarItems.find((item)=>{\n            if (item.href === '/dashboard' && pathname === '/dashboard') {\n                return true;\n            }\n            if (item.href !== '/dashboard' && pathname.startsWith(item.href)) {\n                return true;\n            }\n            return false;\n        });\n        return (currentItem === null || currentItem === void 0 ? void 0 : currentItem.label) || 'AI RecruitPro';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    items: sidebarItems\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: getPageTitle(),\n                        showSearch: showSearch,\n                        onSearch: onSearch,\n                        actions: headerActions\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex-1 overflow-auto p-6', className),\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"p6ZuCIDIAQeeV72fxRZ28CwS/Fo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = Layout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Layout.tsx\n"));

/***/ })

});