/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(dashboard)/jobs/page";
exports.ids = ["app/(dashboard)/jobs/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fjobs%2Fpage&page=%2F(dashboard)%2Fjobs%2Fpage&appPaths=%2F(dashboard)%2Fjobs%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fjobs%2Fpage.tsx&appDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cai-rekruit-pro%5Cai-rekruit-pro%20next%5Cai-rekruit-pro%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cai-rekruit-pro%5Cai-rekruit-pro%20next%5Cai-rekruit-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fjobs%2Fpage&page=%2F(dashboard)%2Fjobs%2Fpage&appPaths=%2F(dashboard)%2Fjobs%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fjobs%2Fpage.tsx&appDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cai-rekruit-pro%5Cai-rekruit-pro%20next%5Cai-rekruit-pro%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cai-rekruit-pro%5Cai-rekruit-pro%20next%5Cai-rekruit-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/layout.tsx */ \"(rsc)/./src/app/(dashboard)/layout.tsx\"));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page8 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/jobs/page.tsx */ \"(rsc)/./src/app/(dashboard)/jobs/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(dashboard)',\n        {\n        children: [\n        'jobs',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\layout.tsx\"],\n'not-found': [module5, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module6, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module7, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(dashboard)/jobs/page\",\n        pathname: \"/jobs\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fjobs%2Fpage&page=%2F(dashboard)%2Fjobs%2Fpage&appPaths=%2F(dashboard)%2Fjobs%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fjobs%2Fpage.tsx&appDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cai-rekruit-pro%5Cai-rekruit-pro%20next%5Cai-rekruit-pro%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cai-rekruit-pro%5Cai-rekruit-pro%20next%5Cai-rekruit-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cjobs%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cjobs%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/jobs/page.tsx */ \"(rsc)/./src/app/(dashboard)/jobs/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q2FpLXJla3J1aXQtcHJvJTVDJTVDYWktcmVrcnVpdC1wcm8lMjBuZXh0JTVDJTVDYWktcmVrcnVpdC1wcm8lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMoZGFzaGJvYXJkKSU1QyU1Q2pvYnMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0xBQW9LIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0c1xcXFxXZWIgRmlsZXNcXFxcbmVhcnRla3BvZFxcXFxhaS1yZWtydWl0LXByb1xcXFxhaS1yZWtydWl0LXBybyBuZXh0XFxcXGFpLXJla3J1aXQtcHJvXFxcXHNyY1xcXFxhcHBcXFxcKGRhc2hib2FyZClcXFxcam9ic1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cjobs%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/layout.tsx */ \"(rsc)/./src/app/(dashboard)/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q2FpLXJla3J1aXQtcHJvJTVDJTVDYWktcmVrcnVpdC1wcm8lMjBuZXh0JTVDJTVDYWktcmVrcnVpdC1wcm8lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMoZGFzaGJvYXJkKSU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRLQUFnSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdHNcXFxcV2ViIEZpbGVzXFxcXG5lYXJ0ZWtwb2RcXFxcYWktcmVrcnVpdC1wcm9cXFxcYWktcmVrcnVpdC1wcm8gbmV4dFxcXFxhaS1yZWtydWl0LXByb1xcXFxzcmNcXFxcYXBwXFxcXChkYXNoYm9hcmQpXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcV2ViIEZpbGVzXFxuZWFydGVrcG9kXFxhaS1yZWtydWl0LXByb1xcYWktcmVrcnVpdC1wcm8gbmV4dFxcYWktcmVrcnVpdC1wcm9cXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/(dashboard)/jobs/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/(dashboard)/jobs/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\jobs\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/(dashboard)/layout.tsx":
/*!****************************************!*\
  !*** ./src/app/(dashboard)/layout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\ai-rekruit-pro next\\ai-rekruit-pro\\src\\app\\(dashboard)\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXFdlYiBGaWxlc1xcbmVhcnRla3BvZFxcYWktcmVrcnVpdC1wcm9cXGFpLXJla3J1aXQtcHJvIG5leHRcXGFpLXJla3J1aXQtcHJvXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTljYjBmYzNmNjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"AI RecruitPro - Intelligent Recruitment Platform\",\n    description: \"AI-powered recruitment and hiring management platform\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcV2ViIEZpbGVzXFxuZWFydGVrcG9kXFxhaS1yZWtydWl0LXByb1xcYWktcmVrcnVpdC1wcm8gbmV4dFxcYWktcmVrcnVpdC1wcm9cXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8gfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBnZWlzdFNhbnMgPSBHZWlzdCh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1zYW5zXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmNvbnN0IGdlaXN0TW9ubyA9IEdlaXN0X01vbm8oe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJBSSBSZWNydWl0UHJvIC0gSW50ZWxsaWdlbnQgUmVjcnVpdG1lbnQgUGxhdGZvcm1cIixcbiAgZGVzY3JpcHRpb246IFwiQUktcG93ZXJlZCByZWNydWl0bWVudCBhbmQgaGlyaW5nIG1hbmFnZW1lbnQgcGxhdGZvcm1cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cjobs%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cjobs%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/jobs/page.tsx */ \"(ssr)/./src/app/(dashboard)/jobs/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q2FpLXJla3J1aXQtcHJvJTVDJTVDYWktcmVrcnVpdC1wcm8lMjBuZXh0JTVDJTVDYWktcmVrcnVpdC1wcm8lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMoZGFzaGJvYXJkKSU1QyU1Q2pvYnMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0xBQW9LIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0c1xcXFxXZWIgRmlsZXNcXFxcbmVhcnRla3BvZFxcXFxhaS1yZWtydWl0LXByb1xcXFxhaS1yZWtydWl0LXBybyBuZXh0XFxcXGFpLXJla3J1aXQtcHJvXFxcXHNyY1xcXFxhcHBcXFxcKGRhc2hib2FyZClcXFxcam9ic1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cjobs%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/layout.tsx */ \"(ssr)/./src/app/(dashboard)/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q2FpLXJla3J1aXQtcHJvJTVDJTVDYWktcmVrcnVpdC1wcm8lMjBuZXh0JTVDJTVDYWktcmVrcnVpdC1wcm8lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMoZGFzaGJvYXJkKSU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRLQUFnSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdHNcXFxcV2ViIEZpbGVzXFxcXG5lYXJ0ZWtwb2RcXFxcYWktcmVrcnVpdC1wcm9cXFxcYWktcmVrcnVpdC1wcm8gbmV4dFxcXFxhaS1yZWtydWl0LXByb1xcXFxzcmNcXFxcYXBwXFxcXChkYXNoYm9hcmQpXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cai-rekruit-pro%5C%5Cai-rekruit-pro%20next%5C%5Cai-rekruit-pro%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(dashboard)/jobs/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/(dashboard)/jobs/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JobsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components */ \"(ssr)/./src/components/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction JobsPage() {\n    const [searchQuery, setSearchQuery] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('');\n    const [statusFilters, setStatusFilters] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const [departmentFilters, setDepartmentFilters] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    // Sample job data\n    const jobs = [\n        {\n            id: '1',\n            title: 'Senior Frontend Developer',\n            department: 'Engineering',\n            location: 'San Francisco, CA',\n            type: 'full-time',\n            status: 'active',\n            description: 'We are looking for an experienced frontend developer to join our team and help build amazing user experiences.',\n            requirements: [\n                'React',\n                'TypeScript',\n                'Next.js',\n                'Tailwind CSS'\n            ],\n            postedDate: new Date('2024-01-15'),\n            closingDate: new Date('2024-02-15'),\n            salary: {\n                min: 120000,\n                max: 160000,\n                currency: 'USD'\n            }\n        },\n        {\n            id: '2',\n            title: 'UX Designer',\n            department: 'Design',\n            location: 'Remote',\n            type: 'full-time',\n            status: 'paused',\n            description: 'Join our design team to create intuitive and beautiful user interfaces.',\n            requirements: [\n                'Figma',\n                'Adobe Creative Suite',\n                'User Research',\n                'Prototyping'\n            ],\n            postedDate: new Date('2024-01-10'),\n            closingDate: new Date('2024-02-10'),\n            salary: {\n                min: 90000,\n                max: 120000,\n                currency: 'USD'\n            }\n        },\n        {\n            id: '3',\n            title: 'Backend Engineer',\n            department: 'Engineering',\n            location: 'New York, NY',\n            type: 'full-time',\n            status: 'active',\n            description: 'Build scalable backend systems and APIs for our growing platform.',\n            requirements: [\n                'Node.js',\n                'Python',\n                'PostgreSQL',\n                'AWS'\n            ],\n            postedDate: new Date('2024-01-20'),\n            closingDate: new Date('2024-02-20'),\n            salary: {\n                min: 130000,\n                max: 170000,\n                currency: 'USD'\n            }\n        }\n    ];\n    const statusOptions = [\n        {\n            value: 'active',\n            label: 'Active',\n            count: 2\n        },\n        {\n            value: 'paused',\n            label: 'Paused',\n            count: 1\n        },\n        {\n            value: 'closed',\n            label: 'Closed',\n            count: 0\n        },\n        {\n            value: 'draft',\n            label: 'Draft',\n            count: 0\n        }\n    ];\n    const departmentOptions = [\n        {\n            value: 'engineering',\n            label: 'Engineering',\n            count: 2\n        },\n        {\n            value: 'design',\n            label: 'Design',\n            count: 1\n        },\n        {\n            value: 'marketing',\n            label: 'Marketing',\n            count: 0\n        },\n        {\n            value: 'sales',\n            label: 'Sales',\n            count: 0\n        }\n    ];\n    const tableColumns = [\n        {\n            key: 'title',\n            title: 'Job Title',\n            sortable: true\n        },\n        {\n            key: 'department',\n            title: 'Department',\n            sortable: true\n        },\n        {\n            key: 'location',\n            title: 'Location',\n            sortable: true\n        },\n        {\n            key: 'status',\n            title: 'Status',\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                    variant: value === 'active' ? 'success' : value === 'paused' ? 'warning' : 'default',\n                    children: value\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'postedDate',\n            title: 'Posted Date',\n            sortable: true\n        }\n    ];\n    const tableData = jobs.map((job)=>({\n            ...job,\n            postedDate: job.postedDate.toLocaleDateString()\n        }));\n    const handleCreateJob = ()=>{\n        console.log('Create new job');\n    };\n    const handleJobAction = (action, job)=>{\n        console.log(`${action} job:`, job);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Job Postings\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage and track your job openings\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"primary\",\n                        onClick: handleCreateJob,\n                        children: \"Post New Job\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                padding: \"md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.SearchBar, {\n                            placeholder: \"Search jobs by title, department, or location...\",\n                            value: searchQuery,\n                            onChange: setSearchQuery,\n                            onSearch: (query)=>console.log('Search:', query)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FilterDropdown, {\n                                    title: \"Status\",\n                                    options: statusOptions,\n                                    selectedValues: statusFilters,\n                                    onChange: setStatusFilters,\n                                    placeholder: \"Filter by status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FilterDropdown, {\n                                    title: \"Department\",\n                                    options: departmentOptions,\n                                    selectedValues: departmentFilters,\n                                    onChange: setDepartmentFilters,\n                                    placeholder: \"Filter by department\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Active Postings\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: jobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.JobPostingCard, {\n                                job: job,\n                                onView: (job)=>handleJobAction('view', job),\n                                onEdit: (job)=>handleJobAction('edit', job),\n                                onDelete: (job)=>handleJobAction('delete', job)\n                            }, job.id, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"All Jobs\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                        columns: tableColumns,\n                        data: tableData,\n                        onSort: (key, direction)=>console.log('Sort:', key, direction),\n                        onRowClick: (row)=>console.log('Row clicked:', row)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\jobs\\\\page.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(dashboard)/jobs/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(dashboard)/layout.tsx":
/*!****************************************!*\
  !*** ./src/app/(dashboard)/layout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components */ \"(ssr)/./src/components/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_1__.Layout, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwLyhkYXNoYm9hcmQpL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFc0M7QUFFdkIsU0FBU0MsZ0JBQWdCLEVBQ3RDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0YsK0NBQU1BO2tCQUNKRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcV2ViIEZpbGVzXFxuZWFydGVrcG9kXFxhaS1yZWtydWl0LXByb1xcYWktcmVrcnVpdC1wcm8gbmV4dFxcYWktcmVrcnVpdC1wcm9cXHNyY1xcYXBwXFwoZGFzaGJvYXJkKVxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IExheW91dCB9IGZyb20gJ0AvY29tcG9uZW50cyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxMYXlvdXQ+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9MYXlvdXQ+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiTGF5b3V0IiwiRGFzaGJvYXJkTGF5b3V0IiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(dashboard)/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/CandidateCard.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/CandidateCard.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n\n\n\n\nconst CandidateCard = ({ candidate, onView, onContact, onScheduleInterview, className })=>{\n    const handleView = ()=>onView?.(candidate);\n    const handleContact = ()=>onContact?.(candidate);\n    const handleScheduleInterview = ()=>onScheduleInterview?.(candidate);\n    const getStatusVariant = (status)=>{\n        switch(status){\n            case 'new':\n                return 'info';\n            case 'screening':\n                return 'warning';\n            case 'interview':\n                return 'default';\n            case 'offer':\n                return 'success';\n            case 'hired':\n                return 'success';\n            case 'rejected':\n                return 'danger';\n            default:\n                return 'default';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('hover:shadow-md transition-shadow', className),\n        padding: \"md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: candidate.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: candidate.avatar,\n                        alt: candidate.name,\n                        className: \"w-12 h-12 rounded-full object-cover\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-600 font-medium text-lg\",\n                            children: candidate.name.split(' ').map((n)=>n[0]).join('')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 truncate\",\n                                            children: candidate.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: candidate.email\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        candidate.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: candidate.phone\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: getStatusVariant(candidate.status),\n                                    size: \"sm\",\n                                    children: candidate.status\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                            name: \"location\",\n                                            size: \"sm\",\n                                            className: \"mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: candidate.location\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mx-2\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                candidate.experience,\n                                                \" years experience\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, undefined),\n                                candidate.skills.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-1\",\n                                    children: [\n                                        candidate.skills.slice(0, 3).map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"default\",\n                                                size: \"sm\",\n                                                children: skill\n                                            }, index, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, undefined)),\n                                        candidate.skills.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"default\",\n                                            size: \"sm\",\n                                            children: [\n                                                \"+\",\n                                                candidate.skills.length - 3,\n                                                \" more\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        \"Applied \",\n                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatRelativeTime)(candidate.appliedDate)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleView,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                                    name: \"eye\",\n                                                    size: \"sm\",\n                                                    className: \"mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"View\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleContact,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                                    name: \"mail\",\n                                                    size: \"sm\",\n                                                    className: \"mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Contact\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        candidate.status === 'screening' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"primary\",\n                                            size: \"sm\",\n                                            onClick: handleScheduleInterview,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                                    name: \"calendar\",\n                                                    size: \"sm\",\n                                                    className: \"mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Interview\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\CandidateCard.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CandidateCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/CandidateCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/JobPostingCard.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/JobPostingCard.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n\n\n\n\nconst JobPostingCard = ({ job, onView, onEdit, onDelete, className })=>{\n    const handleView = ()=>onView?.(job);\n    const handleEdit = ()=>onEdit?.(job);\n    const handleDelete = ()=>onDelete?.(job);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('hover:shadow-md transition-shadow', className),\n        padding: \"md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                children: job.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2\",\n                                children: [\n                                    job.department,\n                                    \" • \",\n                                    job.location\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: job.status === 'active' ? 'success' : job.status === 'paused' ? 'warning' : job.status === 'closed' ? 'danger' : 'default',\n                                        size: \"sm\",\n                                        children: job.status\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"info\",\n                                        size: \"sm\",\n                                        children: job.type\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleView,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                    name: \"eye\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleEdit,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                    name: \"edit\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleDelete,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                    name: \"trash\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-700 line-clamp-2\",\n                        children: job.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    job.salary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: [\n                            job.salary.currency,\n                            \" \",\n                            job.salary.min.toLocaleString(),\n                            \" - \",\n                            job.salary.max.toLocaleString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between text-sm text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Posted \",\n                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatRelativeTime)(job.postedDate)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    job.closingDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Closes \",\n                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatRelativeTime)(job.closingDate)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\JobPostingCard.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JobPostingCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/JobPostingCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/MetricCard.tsx":
/*!*************************************************!*\
  !*** ./src/components/dashboard/MetricCard.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n\n\n\n\nconst MetricCard = ({ metric, className })=>{\n    const getChangeColor = ()=>{\n        if (!metric.change) return '';\n        return metric.changeType === 'increase' ? 'text-green-600' : 'text-red-600';\n    };\n    const getChangeIcon = ()=>{\n        if (!metric.change) return null;\n        return metric.changeType === 'increase' ? '↗' : '↘';\n    };\n    const getCardColor = ()=>{\n        const colors = {\n            blue: 'border-l-blue-500',\n            green: 'border-l-green-500',\n            orange: 'border-l-orange-500',\n            red: 'border-l-red-500',\n            purple: 'border-l-purple-500'\n        };\n        return metric.color ? colors[metric.color] : 'border-l-blue-500';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('border-l-4 hover:shadow-md transition-shadow', getCardColor(), className),\n        padding: \"md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                            children: metric.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: metric.value\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        metric.change && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center mt-2 text-sm', getChangeColor()),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-1\",\n                                    children: getChangeIcon()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        Math.abs(metric.change),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 ml-1\",\n                                    children: \"vs last month\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined),\n                metric.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('p-3 rounded-lg', metric.color === 'blue' && 'bg-blue-100 text-blue-600', metric.color === 'green' && 'bg-green-100 text-green-600', metric.color === 'orange' && 'bg-orange-100 text-orange-600', metric.color === 'red' && 'bg-red-100 text-red-600', metric.color === 'purple' && 'bg-purple-100 text-purple-600', !metric.color && 'bg-blue-100 text-blue-600'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                        name: metric.icon,\n                        size: \"lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\MetricCard.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MetricCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/MetricCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/QuickAction.tsx":
/*!**************************************************!*\
  !*** ./src/components/dashboard/QuickAction.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n\n\n\n\nconst QuickAction = ({ action, className })=>{\n    const getColorClasses = ()=>{\n        const colors = {\n            blue: 'bg-blue-50 hover:bg-blue-100 border-blue-200',\n            green: 'bg-green-50 hover:bg-green-100 border-green-200',\n            orange: 'bg-orange-50 hover:bg-orange-100 border-orange-200',\n            purple: 'bg-purple-50 hover:bg-purple-100 border-purple-200'\n        };\n        return action.color ? colors[action.color] : colors.blue;\n    };\n    const getIconColor = ()=>{\n        const colors = {\n            blue: 'text-blue-600',\n            green: 'text-green-600',\n            orange: 'text-orange-600',\n            purple: 'text-purple-600'\n        };\n        return action.color ? colors[action.color] : colors.blue;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('cursor-pointer transition-all duration-200 hover:shadow-md border-2', getColorClasses(), className),\n        padding: \"lg\",\n        shadow: \"none\",\n        onClick: action.action,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('inline-flex p-3 rounded-full mb-4', getIconColor()),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                        name: action.icon,\n                        size: \"xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\QuickAction.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\QuickAction.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                    children: action.title\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\QuickAction.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: action.description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\QuickAction.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\QuickAction.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\dashboard\\\\QuickAction.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuickAction);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvUXVpY2tBY3Rpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTBCO0FBQ087QUFDWTtBQVE3QyxNQUFNSSxjQUEwQyxDQUFDLEVBQUVDLE1BQU0sRUFBRUMsU0FBUyxFQUFFO0lBQ3BFLE1BQU1DLGtCQUFrQjtRQUN0QixNQUFNQyxTQUFTO1lBQ2JDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLFFBQVE7UUFDVjtRQUNBLE9BQU9QLE9BQU9RLEtBQUssR0FBR0wsTUFBTSxDQUFDSCxPQUFPUSxLQUFLLENBQUMsR0FBR0wsT0FBT0MsSUFBSTtJQUMxRDtJQUVBLE1BQU1LLGVBQWU7UUFDbkIsTUFBTU4sU0FBUztZQUNiQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsUUFBUTtZQUNSQyxRQUFRO1FBQ1Y7UUFDQSxPQUFPUCxPQUFPUSxLQUFLLEdBQUdMLE1BQU0sQ0FBQ0gsT0FBT1EsS0FBSyxDQUFDLEdBQUdMLE9BQU9DLElBQUk7SUFDMUQ7SUFFQSxxQkFDRSw4REFBQ1AsZ0RBQUlBO1FBQ0hJLFdBQVdMLDhDQUFFQSxDQUNYLHVFQUNBTSxtQkFDQUQ7UUFFRlMsU0FBUTtRQUNSQyxRQUFPO1FBQ1BDLFNBQVNaLE9BQU9BLE1BQU07a0JBRXRCLDRFQUFDYTtZQUFJWixXQUFVOzs4QkFDYiw4REFBQ1k7b0JBQUlaLFdBQVdMLDhDQUFFQSxDQUFDLHFDQUFxQ2E7OEJBQ3RELDRFQUFDWCxnREFBSUE7d0JBQUNnQixNQUFNZCxPQUFPZSxJQUFJO3dCQUFFQyxNQUFLOzs7Ozs7Ozs7Ozs4QkFFaEMsOERBQUNDO29CQUFHaEIsV0FBVTs4QkFDWEQsT0FBT2tCLEtBQUs7Ozs7Ozs4QkFFZiw4REFBQ0M7b0JBQUVsQixXQUFVOzhCQUNWRCxPQUFPb0IsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLN0I7QUFFQSxpRUFBZXJCLFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcV2ViIEZpbGVzXFxuZWFydGVrcG9kXFxhaS1yZWtydWl0LXByb1xcYWktcmVrcnVpdC1wcm8gbmV4dFxcYWktcmVrcnVpdC1wcm9cXHNyY1xcY29tcG9uZW50c1xcZGFzaGJvYXJkXFxRdWlja0FjdGlvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuaW1wb3J0IHsgQ2FyZCwgSWNvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aSc7XG5pbXBvcnQgeyBRdWlja0FjdGlvbiBhcyBRdWlja0FjdGlvblR5cGUgfSBmcm9tICdAL3R5cGVzJztcblxuaW50ZXJmYWNlIFF1aWNrQWN0aW9uUHJvcHMge1xuICBhY3Rpb246IFF1aWNrQWN0aW9uVHlwZTtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xufVxuXG5jb25zdCBRdWlja0FjdGlvbjogUmVhY3QuRkM8UXVpY2tBY3Rpb25Qcm9wcz4gPSAoeyBhY3Rpb24sIGNsYXNzTmFtZSB9KSA9PiB7XG4gIGNvbnN0IGdldENvbG9yQ2xhc3NlcyA9ICgpID0+IHtcbiAgICBjb25zdCBjb2xvcnMgPSB7XG4gICAgICBibHVlOiAnYmctYmx1ZS01MCBob3ZlcjpiZy1ibHVlLTEwMCBib3JkZXItYmx1ZS0yMDAnLFxuICAgICAgZ3JlZW46ICdiZy1ncmVlbi01MCBob3ZlcjpiZy1ncmVlbi0xMDAgYm9yZGVyLWdyZWVuLTIwMCcsXG4gICAgICBvcmFuZ2U6ICdiZy1vcmFuZ2UtNTAgaG92ZXI6Ymctb3JhbmdlLTEwMCBib3JkZXItb3JhbmdlLTIwMCcsXG4gICAgICBwdXJwbGU6ICdiZy1wdXJwbGUtNTAgaG92ZXI6YmctcHVycGxlLTEwMCBib3JkZXItcHVycGxlLTIwMCcsXG4gICAgfTtcbiAgICByZXR1cm4gYWN0aW9uLmNvbG9yID8gY29sb3JzW2FjdGlvbi5jb2xvcl0gOiBjb2xvcnMuYmx1ZTtcbiAgfTtcblxuICBjb25zdCBnZXRJY29uQ29sb3IgPSAoKSA9PiB7XG4gICAgY29uc3QgY29sb3JzID0ge1xuICAgICAgYmx1ZTogJ3RleHQtYmx1ZS02MDAnLFxuICAgICAgZ3JlZW46ICd0ZXh0LWdyZWVuLTYwMCcsXG4gICAgICBvcmFuZ2U6ICd0ZXh0LW9yYW5nZS02MDAnLFxuICAgICAgcHVycGxlOiAndGV4dC1wdXJwbGUtNjAwJyxcbiAgICB9O1xuICAgIHJldHVybiBhY3Rpb24uY29sb3IgPyBjb2xvcnNbYWN0aW9uLmNvbG9yXSA6IGNvbG9ycy5ibHVlO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPENhcmRcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICdjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2hhZG93LW1kIGJvcmRlci0yJyxcbiAgICAgICAgZ2V0Q29sb3JDbGFzc2VzKCksXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHBhZGRpbmc9XCJsZ1wiXG4gICAgICBzaGFkb3c9XCJub25lXCJcbiAgICAgIG9uQ2xpY2s9e2FjdGlvbi5hY3Rpb259XG4gICAgPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y24oJ2lubGluZS1mbGV4IHAtMyByb3VuZGVkLWZ1bGwgbWItNCcsIGdldEljb25Db2xvcigpKX0+XG4gICAgICAgICAgPEljb24gbmFtZT17YWN0aW9uLmljb259IHNpemU9XCJ4bFwiIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgIHthY3Rpb24udGl0bGV9XG4gICAgICAgIDwvaDM+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgIHthY3Rpb24uZGVzY3JpcHRpb259XG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuICAgIDwvQ2FyZD5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFF1aWNrQWN0aW9uO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJDYXJkIiwiSWNvbiIsIlF1aWNrQWN0aW9uIiwiYWN0aW9uIiwiY2xhc3NOYW1lIiwiZ2V0Q29sb3JDbGFzc2VzIiwiY29sb3JzIiwiYmx1ZSIsImdyZWVuIiwib3JhbmdlIiwicHVycGxlIiwiY29sb3IiLCJnZXRJY29uQ29sb3IiLCJwYWRkaW5nIiwic2hhZG93Iiwib25DbGljayIsImRpdiIsIm5hbWUiLCJpY29uIiwic2l6ZSIsImgzIiwidGl0bGUiLCJwIiwiZGVzY3JpcHRpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/QuickAction.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/index.ts":
/*!*******************************************!*\
  !*** ./src/components/dashboard/index.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CandidateCard: () => (/* reexport safe */ _CandidateCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   JobPostingCard: () => (/* reexport safe */ _JobPostingCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MetricCard: () => (/* reexport safe */ _MetricCard__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   QuickAction: () => (/* reexport safe */ _QuickAction__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _MetricCard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./MetricCard */ \"(ssr)/./src/components/dashboard/MetricCard.tsx\");\n/* harmony import */ var _JobPostingCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./JobPostingCard */ \"(ssr)/./src/components/dashboard/JobPostingCard.tsx\");\n/* harmony import */ var _CandidateCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CandidateCard */ \"(ssr)/./src/components/dashboard/CandidateCard.tsx\");\n/* harmony import */ var _QuickAction__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QuickAction */ \"(ssr)/./src/components/dashboard/QuickAction.tsx\");\n// Export all dashboard components from a single entry point\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQSw0REFBNEQ7QUFDUDtBQUNRO0FBQ0Y7QUFDSiIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFxXZWIgRmlsZXNcXG5lYXJ0ZWtwb2RcXGFpLXJla3J1aXQtcHJvXFxhaS1yZWtydWl0LXBybyBuZXh0XFxhaS1yZWtydWl0LXByb1xcc3JjXFxjb21wb25lbnRzXFxkYXNoYm9hcmRcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydCBhbGwgZGFzaGJvYXJkIGNvbXBvbmVudHMgZnJvbSBhIHNpbmdsZSBlbnRyeSBwb2ludFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNZXRyaWNDYXJkIH0gZnJvbSAnLi9NZXRyaWNDYXJkJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSm9iUG9zdGluZ0NhcmQgfSBmcm9tICcuL0pvYlBvc3RpbmdDYXJkJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FuZGlkYXRlQ2FyZCB9IGZyb20gJy4vQ2FuZGlkYXRlQ2FyZCc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIFF1aWNrQWN0aW9uIH0gZnJvbSAnLi9RdWlja0FjdGlvbic7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIk1ldHJpY0NhcmQiLCJKb2JQb3N0aW5nQ2FyZCIsIkNhbmRpZGF0ZUNhcmQiLCJRdWlja0FjdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/forms/DataTable.tsx":
/*!********************************************!*\
  !*** ./src/components/forms/DataTable.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n\n\n\n\nconst DataTable = ({ columns, data, loading = false, sortBy, sortDirection = 'asc', onSort, onRowClick, emptyMessage = 'No data available', className })=>{\n    const handleSort = (columnKey)=>{\n        if (!onSort) return;\n        const newDirection = sortBy === columnKey && sortDirection === 'asc' ? 'desc' : 'asc';\n        onSort(columnKey, newDirection);\n    };\n    const renderSortIcon = (columnKey)=>{\n        if (sortBy !== columnKey) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                name: \"chevronDown\",\n                size: \"sm\",\n                className: \"text-gray-300\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                lineNumber: 46,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n            name: \"chevronDown\",\n            size: \"sm\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('text-blue-600', sortDirection === 'asc' && 'transform rotate-180')\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderCellContent = (column, row)=>{\n        const value = row[column.key];\n        if (column.render) {\n            return column.render(value, row);\n        }\n        return value;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('bg-white rounded-lg border border-gray-200', className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin inline-block w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-sm text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('bg-white rounded-lg border border-gray-200 overflow-hidden', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full divide-y divide-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider', column.sortable && 'cursor-pointer hover:bg-gray-100', column.width && `w-${column.width}`),\n                                    onClick: ()=>column.sortable && handleSort(column.key),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: column.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            column.sortable && renderSortIcon(column.key)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, column.key, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"bg-white divide-y divide-gray-200\",\n                        children: data.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                colSpan: columns.length,\n                                className: \"px-6 py-8 text-center text-sm text-gray-500\",\n                                children: emptyMessage\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 15\n                        }, undefined) : data.map((row, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('hover:bg-gray-50', onRowClick && 'cursor-pointer'),\n                                onClick: ()=>onRowClick?.(row),\n                                children: columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                        children: renderCellContent(column, row)\n                                    }, column.key, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 21\n                                    }, undefined))\n                            }, index, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\DataTable.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataTable);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9mb3Jtcy9EYXRhVGFibGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTBCO0FBQ087QUFDcUI7QUFzQnRELE1BQU1HLFlBQXNDLENBQUMsRUFDM0NDLE9BQU8sRUFDUEMsSUFBSSxFQUNKQyxVQUFVLEtBQUssRUFDZkMsTUFBTSxFQUNOQyxnQkFBZ0IsS0FBSyxFQUNyQkMsTUFBTSxFQUNOQyxVQUFVLEVBQ1ZDLGVBQWUsbUJBQW1CLEVBQ2xDQyxTQUFTLEVBQ1Y7SUFDQyxNQUFNQyxhQUFhLENBQUNDO1FBQ2xCLElBQUksQ0FBQ0wsUUFBUTtRQUViLE1BQU1NLGVBQ0pSLFdBQVdPLGFBQWFOLGtCQUFrQixRQUFRLFNBQVM7UUFDN0RDLE9BQU9LLFdBQVdDO0lBQ3BCO0lBRUEsTUFBTUMsaUJBQWlCLENBQUNGO1FBQ3RCLElBQUlQLFdBQVdPLFdBQVc7WUFDeEIscUJBQU8sOERBQUNaLGdEQUFJQTtnQkFBQ2UsTUFBSztnQkFBY0MsTUFBSztnQkFBS04sV0FBVTs7Ozs7O1FBQ3REO1FBRUEscUJBQ0UsOERBQUNWLGdEQUFJQTtZQUNIZSxNQUFLO1lBQ0xDLE1BQUs7WUFDTE4sV0FBV1gsOENBQUVBLENBQ1gsaUJBQ0FPLGtCQUFrQixTQUFTOzs7Ozs7SUFJbkM7SUFFQSxNQUFNVyxvQkFBb0IsQ0FBQ0MsUUFBZ0JDO1FBQ3pDLE1BQU1DLFFBQVFELEdBQUcsQ0FBQ0QsT0FBT0csR0FBRyxDQUFDO1FBRTdCLElBQUlILE9BQU9JLE1BQU0sRUFBRTtZQUNqQixPQUFPSixPQUFPSSxNQUFNLENBQUNGLE9BQU9EO1FBQzlCO1FBRUEsT0FBT0M7SUFDVDtJQUVBLElBQUloQixTQUFTO1FBQ1gscUJBQ0UsOERBQUNtQjtZQUFJYixXQUFXWCw4Q0FBRUEsQ0FBQyw4Q0FBOENXO3NCQUMvRCw0RUFBQ2E7Z0JBQUliLFdBQVU7O2tDQUNiLDhEQUFDYTt3QkFBSWIsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDYzt3QkFBRWQsV0FBVTtrQ0FBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSWxEO0lBRUEscUJBQ0UsOERBQUNhO1FBQUliLFdBQVdYLDhDQUFFQSxDQUFDLDhEQUE4RFc7a0JBQy9FLDRFQUFDYTtZQUFJYixXQUFVO3NCQUNiLDRFQUFDZTtnQkFBTWYsV0FBVTs7a0NBQ2YsOERBQUNnQjt3QkFBTWhCLFdBQVU7a0NBQ2YsNEVBQUNpQjtzQ0FDRXpCLFFBQVEwQixHQUFHLENBQUMsQ0FBQ1YsdUJBQ1osOERBQUNXO29DQUVDbkIsV0FBV1gsOENBQUVBLENBQ1gsa0ZBQ0FtQixPQUFPWSxRQUFRLElBQUksb0NBQ25CWixPQUFPYSxLQUFLLElBQUksQ0FBQyxFQUFFLEVBQUViLE9BQU9hLEtBQUssRUFBRTtvQ0FFckNDLFNBQVMsSUFBTWQsT0FBT1ksUUFBUSxJQUFJbkIsV0FBV08sT0FBT0csR0FBRzs4Q0FFdkQsNEVBQUNFO3dDQUFJYixXQUFVOzswREFDYiw4REFBQ3VCOzBEQUFNZixPQUFPZ0IsS0FBSzs7Ozs7OzRDQUNsQmhCLE9BQU9ZLFFBQVEsSUFBSWhCLGVBQWVJLE9BQU9HLEdBQUc7Ozs7Ozs7bUNBVjFDSCxPQUFPRyxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7a0NBZ0J2Qiw4REFBQ2M7d0JBQU16QixXQUFVO2tDQUNkUCxLQUFLaUMsTUFBTSxLQUFLLGtCQUNmLDhEQUFDVDtzQ0FDQyw0RUFBQ1U7Z0NBQ0NDLFNBQVNwQyxRQUFRa0MsTUFBTTtnQ0FDdkIxQixXQUFVOzBDQUVURDs7Ozs7Ozs7Ozt3Q0FJTE4sS0FBS3lCLEdBQUcsQ0FBQyxDQUFDVCxLQUFLb0Isc0JBQ2IsOERBQUNaO2dDQUVDakIsV0FBV1gsOENBQUVBLENBQ1gsb0JBQ0FTLGNBQWM7Z0NBRWhCd0IsU0FBUyxJQUFNeEIsYUFBYVc7MENBRTNCakIsUUFBUTBCLEdBQUcsQ0FBQyxDQUFDVix1QkFDWiw4REFBQ21CO3dDQUVDM0IsV0FBVTtrREFFVE8sa0JBQWtCQyxRQUFRQzt1Q0FIdEJELE9BQU9HLEdBQUc7Ozs7OytCQVRka0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBdUJ2QjtBQUVBLGlFQUFldEMsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFxXZWIgRmlsZXNcXG5lYXJ0ZWtwb2RcXGFpLXJla3J1aXQtcHJvXFxhaS1yZWtydWl0LXBybyBuZXh0XFxhaS1yZWtydWl0LXByb1xcc3JjXFxjb21wb25lbnRzXFxmb3Jtc1xcRGF0YVRhYmxlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBJY29uLCBCdXR0b24sIEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpJztcblxuaW50ZXJmYWNlIENvbHVtbiB7XG4gIGtleTogc3RyaW5nO1xuICB0aXRsZTogc3RyaW5nO1xuICBzb3J0YWJsZT86IGJvb2xlYW47XG4gIHJlbmRlcj86ICh2YWx1ZTogYW55LCByb3c6IGFueSkgPT4gUmVhY3QuUmVhY3ROb2RlO1xuICB3aWR0aD86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIERhdGFUYWJsZVByb3BzIHtcbiAgY29sdW1uczogQ29sdW1uW107XG4gIGRhdGE6IGFueVtdO1xuICBsb2FkaW5nPzogYm9vbGVhbjtcbiAgc29ydEJ5Pzogc3RyaW5nO1xuICBzb3J0RGlyZWN0aW9uPzogJ2FzYycgfCAnZGVzYyc7XG4gIG9uU29ydD86IChrZXk6IHN0cmluZywgZGlyZWN0aW9uOiAnYXNjJyB8ICdkZXNjJykgPT4gdm9pZDtcbiAgb25Sb3dDbGljaz86IChyb3c6IGFueSkgPT4gdm9pZDtcbiAgZW1wdHlNZXNzYWdlPzogc3RyaW5nO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmNvbnN0IERhdGFUYWJsZTogUmVhY3QuRkM8RGF0YVRhYmxlUHJvcHM+ID0gKHtcbiAgY29sdW1ucyxcbiAgZGF0YSxcbiAgbG9hZGluZyA9IGZhbHNlLFxuICBzb3J0QnksXG4gIHNvcnREaXJlY3Rpb24gPSAnYXNjJyxcbiAgb25Tb3J0LFxuICBvblJvd0NsaWNrLFxuICBlbXB0eU1lc3NhZ2UgPSAnTm8gZGF0YSBhdmFpbGFibGUnLFxuICBjbGFzc05hbWUsXG59KSA9PiB7XG4gIGNvbnN0IGhhbmRsZVNvcnQgPSAoY29sdW1uS2V5OiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIW9uU29ydCkgcmV0dXJuO1xuICAgIFxuICAgIGNvbnN0IG5ld0RpcmVjdGlvbiA9IFxuICAgICAgc29ydEJ5ID09PSBjb2x1bW5LZXkgJiYgc29ydERpcmVjdGlvbiA9PT0gJ2FzYycgPyAnZGVzYycgOiAnYXNjJztcbiAgICBvblNvcnQoY29sdW1uS2V5LCBuZXdEaXJlY3Rpb24pO1xuICB9O1xuXG4gIGNvbnN0IHJlbmRlclNvcnRJY29uID0gKGNvbHVtbktleTogc3RyaW5nKSA9PiB7XG4gICAgaWYgKHNvcnRCeSAhPT0gY29sdW1uS2V5KSB7XG4gICAgICByZXR1cm4gPEljb24gbmFtZT1cImNoZXZyb25Eb3duXCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiIC8+O1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gKFxuICAgICAgPEljb24gXG4gICAgICAgIG5hbWU9XCJjaGV2cm9uRG93blwiIFxuICAgICAgICBzaXplPVwic21cIiBcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAndGV4dC1ibHVlLTYwMCcsXG4gICAgICAgICAgc29ydERpcmVjdGlvbiA9PT0gJ2FzYycgJiYgJ3RyYW5zZm9ybSByb3RhdGUtMTgwJ1xuICAgICAgICApfSBcbiAgICAgIC8+XG4gICAgKTtcbiAgfTtcblxuICBjb25zdCByZW5kZXJDZWxsQ29udGVudCA9IChjb2x1bW46IENvbHVtbiwgcm93OiBhbnkpID0+IHtcbiAgICBjb25zdCB2YWx1ZSA9IHJvd1tjb2x1bW4ua2V5XTtcbiAgICBcbiAgICBpZiAoY29sdW1uLnJlbmRlcikge1xuICAgICAgcmV0dXJuIGNvbHVtbi5yZW5kZXIodmFsdWUsIHJvdyk7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiB2YWx1ZTtcbiAgfTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17Y24oJ2JnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCcsIGNsYXNzTmFtZSl9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtOCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIGlubGluZS1ibG9jayB3LTYgaC02IGJvcmRlci0yIGJvcmRlci1ibHVlLTYwMCBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcuLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2NuKCdiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgb3ZlcmZsb3ctaGlkZGVuJywgY2xhc3NOYW1lKX0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICA8dGhlYWQgY2xhc3NOYW1lPVwiYmctZ3JheS01MFwiPlxuICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICB7Y29sdW1ucy5tYXAoKGNvbHVtbikgPT4gKFxuICAgICAgICAgICAgICAgIDx0aFxuICAgICAgICAgICAgICAgICAga2V5PXtjb2x1bW4ua2V5fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgJ3B4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlcicsXG4gICAgICAgICAgICAgICAgICAgIGNvbHVtbi5zb3J0YWJsZSAmJiAnY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctZ3JheS0xMDAnLFxuICAgICAgICAgICAgICAgICAgICBjb2x1bW4ud2lkdGggJiYgYHctJHtjb2x1bW4ud2lkdGh9YFxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGNvbHVtbi5zb3J0YWJsZSAmJiBoYW5kbGVTb3J0KGNvbHVtbi5rZXkpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPntjb2x1bW4udGl0bGV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICB7Y29sdW1uLnNvcnRhYmxlICYmIHJlbmRlclNvcnRJY29uKGNvbHVtbi5rZXkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L3RyPlxuICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgPHRib2R5IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxuICAgICAgICAgICAge2RhdGEubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgPHRkIFxuICAgICAgICAgICAgICAgICAgY29sU3Bhbj17Y29sdW1ucy5sZW5ndGh9IFxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNiBweS04IHRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2VtcHR5TWVzc2FnZX1cbiAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgZGF0YS5tYXAoKHJvdywgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8dHJcbiAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAnaG92ZXI6YmctZ3JheS01MCcsXG4gICAgICAgICAgICAgICAgICAgIG9uUm93Q2xpY2sgJiYgJ2N1cnNvci1wb2ludGVyJ1xuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uUm93Q2xpY2s/Lihyb3cpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtjb2x1bW5zLm1hcCgoY29sdW1uKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDx0ZFxuICAgICAgICAgICAgICAgICAgICAgIGtleT17Y29sdW1uLmtleX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtyZW5kZXJDZWxsQ29udGVudChjb2x1bW4sIHJvdyl9XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICA8L3RhYmxlPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBEYXRhVGFibGU7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkljb24iLCJEYXRhVGFibGUiLCJjb2x1bW5zIiwiZGF0YSIsImxvYWRpbmciLCJzb3J0QnkiLCJzb3J0RGlyZWN0aW9uIiwib25Tb3J0Iiwib25Sb3dDbGljayIsImVtcHR5TWVzc2FnZSIsImNsYXNzTmFtZSIsImhhbmRsZVNvcnQiLCJjb2x1bW5LZXkiLCJuZXdEaXJlY3Rpb24iLCJyZW5kZXJTb3J0SWNvbiIsIm5hbWUiLCJzaXplIiwicmVuZGVyQ2VsbENvbnRlbnQiLCJjb2x1bW4iLCJyb3ciLCJ2YWx1ZSIsImtleSIsInJlbmRlciIsImRpdiIsInAiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJtYXAiLCJ0aCIsInNvcnRhYmxlIiwid2lkdGgiLCJvbkNsaWNrIiwic3BhbiIsInRpdGxlIiwidGJvZHkiLCJsZW5ndGgiLCJ0ZCIsImNvbFNwYW4iLCJpbmRleCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/forms/DataTable.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/forms/FilterDropdown.tsx":
/*!*************************************************!*\
  !*** ./src/components/forms/FilterDropdown.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n\n\n\n\nconst FilterDropdown = ({ title, options, selectedValues, onChange, placeholder = 'Select options...', className })=>{\n    const [isOpen, setIsOpen] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const dropdownRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"FilterDropdown.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"FilterDropdown.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"FilterDropdown.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"FilterDropdown.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"FilterDropdown.useEffect\"];\n        }\n    }[\"FilterDropdown.useEffect\"], []);\n    const handleToggle = ()=>{\n        setIsOpen(!isOpen);\n    };\n    const handleOptionChange = (value)=>{\n        const newSelectedValues = selectedValues.includes(value) ? selectedValues.filter((v)=>v !== value) : [\n            ...selectedValues,\n            value\n        ];\n        onChange(newSelectedValues);\n    };\n    const handleClearAll = ()=>{\n        onChange([]);\n    };\n    const getDisplayText = ()=>{\n        if (selectedValues.length === 0) return placeholder;\n        if (selectedValues.length === 1) {\n            const option = options.find((opt)=>opt.value === selectedValues[0]);\n            return option?.label || selectedValues[0];\n        }\n        return `${selectedValues.length} selected`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('relative', className),\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleToggle,\n                className: \"w-full flex items-center justify-between px-3 py-2 text-left bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-sm text-gray-600 truncate\",\n                                children: getDisplayText()\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                        name: \"chevronDown\",\n                        size: \"sm\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('text-gray-400 transition-transform', isOpen && 'transform rotate-180')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2\",\n                    children: [\n                        selectedValues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClearAll,\n                            className: \"w-full text-left px-2 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded\",\n                            children: \"Clear all\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-60 overflow-y-auto\",\n                            children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center px-2 py-2 hover:bg-gray-50 rounded cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedValues.includes(option.value),\n                                            onChange: ()=>handleOptionChange(option.value),\n                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-gray-700 flex-1\",\n                                            children: option.label\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        option.count !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full\",\n                                            children: option.count\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, option.value, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FilterDropdown.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FilterDropdown);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/forms/FilterDropdown.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/forms/FormField.tsx":
/*!********************************************!*\
  !*** ./src/components/forms/FormField.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n\n\n\n\nconst FormField = ({ label, name, type = 'text', value, onChange, placeholder, required = false, disabled = false, error, helpText, options = [], rows = 3, className })=>{\n    const handleChange = (newValue)=>{\n        onChange(name, newValue);\n    };\n    const renderInput = ()=>{\n        const baseClasses = 'block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm';\n        const errorClasses = 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500';\n        const disabledClasses = 'bg-gray-50 text-gray-500 cursor-not-allowed';\n        const inputClasses = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, error && errorClasses, disabled && disabledClasses);\n        switch(type){\n            case 'textarea':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    id: name,\n                    name: name,\n                    value: value,\n                    onChange: (e)=>handleChange(e.target.value),\n                    placeholder: placeholder,\n                    disabled: disabled,\n                    rows: rows,\n                    className: inputClasses\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, undefined);\n            case 'select':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                    id: name,\n                    name: name,\n                    value: value,\n                    onChange: (e)=>handleChange(e.target.value),\n                    disabled: disabled,\n                    className: inputClasses,\n                    children: [\n                        placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                            value: \"\",\n                            disabled: true,\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 15\n                        }, undefined),\n                        options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: option.value,\n                                children: option.label\n                            }, option.value, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: type,\n                    id: name,\n                    name: name,\n                    value: value,\n                    onChange: (e)=>handleChange(e.target.value),\n                    placeholder: placeholder,\n                    disabled: disabled,\n                    className: inputClasses\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('space-y-1', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: name,\n                className: \"block text-sm font-medium text-gray-700\",\n                children: [\n                    label,\n                    required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-500 ml-1\",\n                        children: \"*\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 22\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            renderInput(),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mt-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                        name: \"alert\",\n                        size: \"sm\",\n                        className: \"text-red-500 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, undefined),\n            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: helpText\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\FormField.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FormField);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/forms/FormField.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/forms/SearchBar.tsx":
/*!********************************************!*\
  !*** ./src/components/forms/SearchBar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n\n\n\n\nconst SearchBar = ({ placeholder = 'Search...', value = '', onChange, onSearch, showFilters = true, onFilterClick, className })=>{\n    const [searchValue, setSearchValue] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(value);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"SearchBar.useEffect\": ()=>{\n            setSearchValue(value);\n        }\n    }[\"SearchBar.useEffect\"], [\n        value\n    ]);\n    const handleInputChange = (newValue)=>{\n        setSearchValue(newValue);\n        onChange?.(newValue);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter') {\n            onSearch?.(searchValue);\n        }\n    };\n    const handleSearchClick = ()=>{\n        onSearch?.(searchValue);\n    };\n    const handleClear = ()=>{\n        setSearchValue('');\n        onChange?.('');\n        onSearch?.('');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center space-x-2', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                            name: \"search\",\n                            size: \"sm\",\n                            className: \"text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: placeholder,\n                        value: searchValue,\n                        onChange: (e)=>handleInputChange(e.target.value),\n                        onKeyPress: handleKeyPress,\n                        className: \"block w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, undefined),\n                    searchValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClear,\n                        className: \"absolute inset-y-0 right-8 flex items-center pr-2 text-gray-400 hover:text-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                            name: \"x\",\n                            size: \"sm\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSearchClick,\n                        className: \"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-blue-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                            name: \"search\",\n                            size: \"sm\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onFilterClick,\n                className: \"inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                        name: \"filter\",\n                        size: \"sm\",\n                        className: \"mr-2\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, undefined),\n                    \"Filters\"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\forms\\\\SearchBar.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/forms/SearchBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/forms/index.ts":
/*!***************************************!*\
  !*** ./src/components/forms/index.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataTable: () => (/* reexport safe */ _DataTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   FilterDropdown: () => (/* reexport safe */ _FilterDropdown__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   FormField: () => (/* reexport safe */ _FormField__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   SearchBar: () => (/* reexport safe */ _SearchBar__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _SearchBar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SearchBar */ \"(ssr)/./src/components/forms/SearchBar.tsx\");\n/* harmony import */ var _FilterDropdown__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./FilterDropdown */ \"(ssr)/./src/components/forms/FilterDropdown.tsx\");\n/* harmony import */ var _DataTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DataTable */ \"(ssr)/./src/components/forms/DataTable.tsx\");\n/* harmony import */ var _FormField__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FormField */ \"(ssr)/./src/components/forms/FormField.tsx\");\n// Export all form components from a single entry point\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9mb3Jtcy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBLHVEQUF1RDtBQUNKO0FBQ1U7QUFDVjtBQUNBIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXFdlYiBGaWxlc1xcbmVhcnRla3BvZFxcYWktcmVrcnVpdC1wcm9cXGFpLXJla3J1aXQtcHJvIG5leHRcXGFpLXJla3J1aXQtcHJvXFxzcmNcXGNvbXBvbmVudHNcXGZvcm1zXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnQgYWxsIGZvcm0gY29tcG9uZW50cyBmcm9tIGEgc2luZ2xlIGVudHJ5IHBvaW50XG5leHBvcnQgeyBkZWZhdWx0IGFzIFNlYXJjaEJhciB9IGZyb20gJy4vU2VhcmNoQmFyJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRmlsdGVyRHJvcGRvd24gfSBmcm9tICcuL0ZpbHRlckRyb3Bkb3duJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRGF0YVRhYmxlIH0gZnJvbSAnLi9EYXRhVGFibGUnO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBGb3JtRmllbGQgfSBmcm9tICcuL0Zvcm1GaWVsZCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIlNlYXJjaEJhciIsIkZpbHRlckRyb3Bkb3duIiwiRGF0YVRhYmxlIiwiRm9ybUZpZWxkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/forms/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/index.ts":
/*!*********************************!*\
  !*** ./src/components/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_0__.Badge),\n/* harmony export */   Button: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_0__.Button),\n/* harmony export */   CandidateCard: () => (/* reexport safe */ _dashboard__WEBPACK_IMPORTED_MODULE_2__.CandidateCard),\n/* harmony export */   Card: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_0__.Card),\n/* harmony export */   DataTable: () => (/* reexport safe */ _forms__WEBPACK_IMPORTED_MODULE_3__.DataTable),\n/* harmony export */   FilterDropdown: () => (/* reexport safe */ _forms__WEBPACK_IMPORTED_MODULE_3__.FilterDropdown),\n/* harmony export */   FormField: () => (/* reexport safe */ _forms__WEBPACK_IMPORTED_MODULE_3__.FormField),\n/* harmony export */   Header: () => (/* reexport safe */ _layout__WEBPACK_IMPORTED_MODULE_1__.Header),\n/* harmony export */   Icon: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_0__.Icon),\n/* harmony export */   Input: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_0__.Input),\n/* harmony export */   JobPostingCard: () => (/* reexport safe */ _dashboard__WEBPACK_IMPORTED_MODULE_2__.JobPostingCard),\n/* harmony export */   Layout: () => (/* reexport safe */ _layout__WEBPACK_IMPORTED_MODULE_1__.Layout),\n/* harmony export */   MetricCard: () => (/* reexport safe */ _dashboard__WEBPACK_IMPORTED_MODULE_2__.MetricCard),\n/* harmony export */   QuickAction: () => (/* reexport safe */ _dashboard__WEBPACK_IMPORTED_MODULE_2__.QuickAction),\n/* harmony export */   SearchBar: () => (/* reexport safe */ _forms__WEBPACK_IMPORTED_MODULE_3__.SearchBar),\n/* harmony export */   Sidebar: () => (/* reexport safe */ _layout__WEBPACK_IMPORTED_MODULE_1__.Sidebar)\n/* harmony export */ });\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ui */ \"(ssr)/./src/components/ui/index.ts\");\n/* harmony import */ var _layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./layout */ \"(ssr)/./src/components/layout/index.ts\");\n/* harmony import */ var _dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dashboard */ \"(ssr)/./src/components/dashboard/index.ts\");\n/* harmony import */ var _forms__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./forms */ \"(ssr)/./src/components/forms/index.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../types */ \"(ssr)/./src/types/index.ts\");\n// Main component library export file\n// This provides a single entry point for all components\n// UI Components\n\n// Layout Components  \n\n// Dashboard Components\n\n// Form Components\n\n// Re-export types for convenience\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxxQ0FBcUM7QUFDckMsd0RBQXdEO0FBRXhELGdCQUFnQjtBQUNLO0FBRXJCLHNCQUFzQjtBQUNHO0FBRXpCLHVCQUF1QjtBQUNLO0FBRTVCLGtCQUFrQjtBQUNNO0FBRXhCLGtDQUFrQztBQUNUIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXFdlYiBGaWxlc1xcbmVhcnRla3BvZFxcYWktcmVrcnVpdC1wcm9cXGFpLXJla3J1aXQtcHJvIG5leHRcXGFpLXJla3J1aXQtcHJvXFxzcmNcXGNvbXBvbmVudHNcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE1haW4gY29tcG9uZW50IGxpYnJhcnkgZXhwb3J0IGZpbGVcbi8vIFRoaXMgcHJvdmlkZXMgYSBzaW5nbGUgZW50cnkgcG9pbnQgZm9yIGFsbCBjb21wb25lbnRzXG5cbi8vIFVJIENvbXBvbmVudHNcbmV4cG9ydCAqIGZyb20gJy4vdWknO1xuXG4vLyBMYXlvdXQgQ29tcG9uZW50cyAgXG5leHBvcnQgKiBmcm9tICcuL2xheW91dCc7XG5cbi8vIERhc2hib2FyZCBDb21wb25lbnRzXG5leHBvcnQgKiBmcm9tICcuL2Rhc2hib2FyZCc7XG5cbi8vIEZvcm0gQ29tcG9uZW50c1xuZXhwb3J0ICogZnJvbSAnLi9mb3Jtcyc7XG5cbi8vIFJlLWV4cG9ydCB0eXBlcyBmb3IgY29udmVuaWVuY2VcbmV4cG9ydCAqIGZyb20gJy4uL3R5cGVzJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n\n\n\n\nconst Header = ({ title, showSearch = true, onSearch, actions, className })=>{\n    const [searchQuery, setSearchQuery] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('');\n    const handleSearchChange = (value)=>{\n        setSearchQuery(value);\n        if (onSearch) {\n            onSearch(value);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('bg-white border-b border-gray-200 px-6 py-4', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-6\",\n                    children: [\n                        title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, undefined),\n                        showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                        name: \"search\",\n                                        size: \"sm\",\n                                        className: \"text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    type: \"search\",\n                                    placeholder: \"Search candidates, jobs...\",\n                                    value: searchQuery,\n                                    onChange: handleSearchChange,\n                                    className: \"pl-10 w-80\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        actions,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"relative p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                    name: \"bell\",\n                                    size: \"md\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: \"JD\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                        name: \"chevronDown\",\n                                        size: \"sm\",\n                                        className: \"text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Layout.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Layout.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Layout = ({ children, title, showSearch = true, onSearch, headerActions, className })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Default sidebar navigation items\n    const sidebarItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: 'dashboard',\n            href: '/dashboard',\n            description: 'Overview and key metrics'\n        },\n        {\n            id: 'jobs',\n            label: 'Jobs',\n            icon: 'jobs',\n            href: '/jobs',\n            badge: '5',\n            description: 'Manage job postings'\n        },\n        {\n            id: 'candidates',\n            label: 'Candidates',\n            icon: 'candidates',\n            href: '/candidates',\n            description: 'View and manage candidates'\n        },\n        {\n            id: 'resume-analysis',\n            label: 'Resume Analysis',\n            icon: 'analytics',\n            href: '/resume-analysis',\n            description: 'AI-powered resume analysis'\n        },\n        {\n            id: 'analytics',\n            label: 'Analytics',\n            icon: 'analytics',\n            href: '/analytics',\n            description: 'Recruitment analytics and reports'\n        },\n        {\n            id: 'calendar',\n            label: 'Calendar',\n            icon: 'calendar',\n            href: '/calendar',\n            description: 'Schedule and manage interviews'\n        },\n        {\n            id: 'profile',\n            label: 'Profile',\n            icon: 'settings',\n            href: '/profile',\n            description: 'Your profile settings'\n        },\n        {\n            id: 'settings',\n            label: 'Settings',\n            icon: 'settings',\n            href: '/settings',\n            description: 'Application settings'\n        }\n    ];\n    // Get page title based on current route\n    const getPageTitle = ()=>{\n        if (title) return title;\n        const currentItem = sidebarItems.find((item)=>{\n            if (item.href === '/dashboard' && pathname === '/dashboard') {\n                return true;\n            }\n            if (item.href !== '/dashboard' && pathname.startsWith(item.href)) {\n                return true;\n            }\n            return false;\n        });\n        return currentItem?.label || 'AI RecruitPro';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    items: sidebarItems\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: getPageTitle(),\n                        showSearch: showSearch,\n                        onSearch: onSearch,\n                        actions: headerActions\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex-1 overflow-auto p-6', className),\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Sidebar = ({ items, className })=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const handleItemClick = (item)=>{\n        router.push(item.href);\n    };\n    const isActive = (href)=>{\n        // Handle exact match for dashboard\n        if (href === '/dashboard' && pathname === '/dashboard') {\n            return true;\n        }\n        // Handle other routes\n        if (href !== '/dashboard' && pathname.startsWith(href)) {\n            return true;\n        }\n        return false;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('w-64 bg-white border-r border-gray-200 h-full', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-sm\",\n                                children: \"RP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"RecruitPro AI\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-2\",\n                    children: items.map((item)=>{\n                        const active = isActive(item.href);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleItemClick(item),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors', active ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-100'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                                                name: item.icon,\n                                                size: \"sm\",\n                                                className: active ? 'text-blue-700' : 'text-gray-500'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded-full\",\n                                        children: item.badge\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.id, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 text-sm font-medium\",\n                                children: \"JD\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-900 truncate\",\n                                    children: \"John Doe\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 truncate\",\n                                    children: \"HR Manager\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                                name: \"settings\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/index.ts":
/*!****************************************!*\
  !*** ./src/components/layout/index.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* reexport safe */ _Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Layout: () => (/* reexport safe */ _Layout__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Sidebar: () => (/* reexport safe */ _Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Layout__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Layout */ \"(ssr)/./src/components/layout/Layout.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n// Export all layout components from a single entry point\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUEseURBQXlEO0FBQ1o7QUFDRTtBQUNGIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXFdlYiBGaWxlc1xcbmVhcnRla3BvZFxcYWktcmVrcnVpdC1wcm9cXGFpLXJla3J1aXQtcHJvIG5leHRcXGFpLXJla3J1aXQtcHJvXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0IGFsbCBsYXlvdXQgY29tcG9uZW50cyBmcm9tIGEgc2luZ2xlIGVudHJ5IHBvaW50XG5leHBvcnQgeyBkZWZhdWx0IGFzIExheW91dCB9IGZyb20gJy4vTGF5b3V0JztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2lkZWJhciB9IGZyb20gJy4vU2lkZWJhcic7XG5leHBvcnQgeyBkZWZhdWx0IGFzIEhlYWRlciB9IGZyb20gJy4vSGVhZGVyJztcbiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwiTGF5b3V0IiwiU2lkZWJhciIsIkhlYWRlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Badge = ({ children, variant = 'default', size = 'md', className, ...props })=>{\n    const baseStyles = 'inline-flex items-center font-medium rounded-full';\n    const variants = {\n        default: 'bg-gray-100 text-gray-800',\n        success: 'bg-green-100 text-green-800',\n        warning: 'bg-yellow-100 text-yellow-800',\n        danger: 'bg-red-100 text-red-800',\n        info: 'bg-blue-100 text-blue-800'\n    };\n    const sizes = {\n        sm: 'px-2 py-0.5 text-xs',\n        md: 'px-2.5 py-0.5 text-sm',\n        lg: 'px-3 py-1 text-sm'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseStyles, variants[variant], sizes[size], className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Badge.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Badge);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9CYWRnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQjtBQUNPO0FBR2pDLE1BQU1FLFFBQThCLENBQUMsRUFDbkNDLFFBQVEsRUFDUkMsVUFBVSxTQUFTLEVBQ25CQyxPQUFPLElBQUksRUFDWEMsU0FBUyxFQUNULEdBQUdDLE9BQ0o7SUFDQyxNQUFNQyxhQUFhO0lBRW5CLE1BQU1DLFdBQVc7UUFDZkMsU0FBUztRQUNUQyxTQUFTO1FBQ1RDLFNBQVM7UUFDVEMsUUFBUTtRQUNSQyxNQUFNO0lBQ1I7SUFFQSxNQUFNQyxRQUFRO1FBQ1pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxxQkFDRSw4REFBQ0M7UUFDQ2IsV0FBV0wsOENBQUVBLENBQ1hPLFlBQ0FDLFFBQVEsQ0FBQ0wsUUFBUSxFQUNqQlcsS0FBSyxDQUFDVixLQUFLLEVBQ1hDO1FBRUQsR0FBR0MsS0FBSztrQkFFUko7Ozs7OztBQUdQO0FBRUEsaUVBQWVELEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcV2ViIEZpbGVzXFxuZWFydGVrcG9kXFxhaS1yZWtydWl0LXByb1xcYWktcmVrcnVpdC1wcm8gbmV4dFxcYWktcmVrcnVpdC1wcm9cXHNyY1xcY29tcG9uZW50c1xcdWlcXEJhZGdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBCYWRnZVByb3BzIH0gZnJvbSAnQC90eXBlcyc7XG5cbmNvbnN0IEJhZGdlOiBSZWFjdC5GQzxCYWRnZVByb3BzPiA9ICh7XG4gIGNoaWxkcmVuLFxuICB2YXJpYW50ID0gJ2RlZmF1bHQnLFxuICBzaXplID0gJ21kJyxcbiAgY2xhc3NOYW1lLFxuICAuLi5wcm9wc1xufSkgPT4ge1xuICBjb25zdCBiYXNlU3R5bGVzID0gJ2lubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBmb250LW1lZGl1bSByb3VuZGVkLWZ1bGwnO1xuICBcbiAgY29uc3QgdmFyaWFudHMgPSB7XG4gICAgZGVmYXVsdDogJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnLFxuICAgIHN1Y2Nlc3M6ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnLFxuICAgIHdhcm5pbmc6ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCcsXG4gICAgZGFuZ2VyOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnLFxuICAgIGluZm86ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJyxcbiAgfTtcbiAgXG4gIGNvbnN0IHNpemVzID0ge1xuICAgIHNtOiAncHgtMiBweS0wLjUgdGV4dC14cycsXG4gICAgbWQ6ICdweC0yLjUgcHktMC41IHRleHQtc20nLFxuICAgIGxnOiAncHgtMyBweS0xIHRleHQtc20nLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPHNwYW5cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIGJhc2VTdHlsZXMsXG4gICAgICAgIHZhcmlhbnRzW3ZhcmlhbnRdLFxuICAgICAgICBzaXplc1tzaXplXSxcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L3NwYW4+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBCYWRnZTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiQmFkZ2UiLCJjaGlsZHJlbiIsInZhcmlhbnQiLCJzaXplIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJiYXNlU3R5bGVzIiwidmFyaWFudHMiLCJkZWZhdWx0Iiwic3VjY2VzcyIsIndhcm5pbmciLCJkYW5nZXIiLCJpbmZvIiwic2l6ZXMiLCJzbSIsIm1kIiwibGciLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = ({ children, variant = 'primary', size = 'md', disabled = false, loading = false, onClick, type = 'button', className, ...props })=>{\n    const baseStyles = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variants = {\n        primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n        secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500',\n        outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n        ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n        danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'\n    };\n    const sizes = {\n        sm: 'px-3 py-1.5 text-sm',\n        md: 'px-4 py-2 text-sm',\n        lg: 'px-6 py-3 text-base'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled || loading,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseStyles, variants[variant], sizes[size], className),\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = ({ children, title, subtitle, padding = 'md', shadow = 'sm', className, ...props })=>{\n    const baseStyles = 'bg-white rounded-lg border border-gray-200';\n    const paddings = {\n        none: '',\n        sm: 'p-3',\n        md: 'p-4',\n        lg: 'p-6'\n    };\n    const shadows = {\n        none: '',\n        sm: 'shadow-sm',\n        md: 'shadow-md',\n        lg: 'shadow-lg'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseStyles, paddings[padding], shadows[shadow], className),\n        ...props,\n        children: [\n            (title || subtitle) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 13\n                    }, undefined),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n// Card sub-components for better composition\nconst CardHeader = ({ children, className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('px-4 py-3 border-b border-gray-200', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined);\nconst CardBody = ({ children, className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('p-4', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 69,\n        columnNumber: 3\n    }, undefined);\nconst CardFooter = ({ children, className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('px-4 py-3 border-t border-gray-200 bg-gray-50 rounded-b-lg', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, undefined);\nCard.Header = CardHeader;\nCard.Body = CardBody;\nCard.Footer = CardFooter;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Icon.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Icon.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Icon = ({ name, size = 'md', className })=>{\n    const sizes = {\n        sm: 'w-4 h-4',\n        md: 'w-5 h-5',\n        lg: 'w-6 h-6',\n        xl: 'w-8 h-8'\n    };\n    // Common SVG icons used in recruitment dashboards\n    const icons = {\n        dashboard: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v0a2 2 0 01-2 2H10a2 2 0 01-2-2v0z\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined),\n        jobs: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2h8z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined),\n        candidates: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined),\n        analytics: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined),\n        calendar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined),\n        settings: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, undefined),\n        search: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, undefined),\n        plus: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, undefined),\n        upload: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, undefined),\n        download: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined),\n        filter: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, undefined),\n        chevronDown: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M19 9l-7 7-7-7\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined),\n        chevronRight: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 5l7 7-7 7\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, undefined),\n        eye: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, undefined),\n        edit: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, undefined),\n        trash: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined),\n        bell: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, undefined),\n        location: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, undefined),\n        mail: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, undefined),\n        x: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, undefined),\n        alert: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, undefined),\n        file: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, undefined),\n        check: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, undefined),\n        chevronLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 19l-7-7 7-7\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, undefined),\n        clock: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, undefined),\n        link: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, undefined),\n        shield: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, undefined),\n        'credit-card': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, undefined),\n        message: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, undefined)\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(sizes[size], className),\n        children: icons[name] || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n                lineNumber: 175,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n            lineNumber: 174,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Icon.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Icon);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Icon.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = ({ type = 'text', placeholder, value, onChange, disabled = false, error, label, required = false, className, ...props })=>{\n    const baseStyles = 'block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm';\n    const errorStyles = 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500';\n    const disabledStyles = 'bg-gray-50 text-gray-500 cursor-not-allowed';\n    const handleChange = (e)=>{\n        if (onChange) {\n            onChange(e.target.value);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\n                    label,\n                    required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-500 ml-1\",\n                        children: \"*\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 24\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: type,\n                placeholder: placeholder,\n                value: value,\n                onChange: handleChange,\n                disabled: disabled,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseStyles, error && errorStyles, disabled && disabledStyles, className),\n                ...props\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Input);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/index.ts":
/*!************************************!*\
  !*** ./src/components/ui/index.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* reexport safe */ _Badge__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Button: () => (/* reexport safe */ _Button__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Card: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Icon: () => (/* reexport safe */ _Icon__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Input: () => (/* reexport safe */ _Input__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _Badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Badge */ \"(ssr)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _Input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Input */ \"(ssr)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _Icon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Icon */ \"(ssr)/./src/components/ui/Icon.tsx\");\n// Export all UI components from a single entry point\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQUEscURBQXFEO0FBQ1I7QUFDSjtBQUNFO0FBQ0E7QUFDRiIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFxXZWIgRmlsZXNcXG5lYXJ0ZWtwb2RcXGFpLXJla3J1aXQtcHJvXFxhaS1yZWtydWl0LXBybyBuZXh0XFxhaS1yZWtydWl0LXByb1xcc3JjXFxjb21wb25lbnRzXFx1aVxcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0IGFsbCBVSSBjb21wb25lbnRzIGZyb20gYSBzaW5nbGUgZW50cnkgcG9pbnRcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQnV0dG9uIH0gZnJvbSAnLi9CdXR0b24nO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYXJkIH0gZnJvbSAnLi9DYXJkJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFkZ2UgfSBmcm9tICcuL0JhZGdlJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSW5wdXQgfSBmcm9tICcuL0lucHV0JztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSWNvbiB9IGZyb20gJy4vSWNvbic7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIkJ1dHRvbiIsIkNhcmQiLCJCYWRnZSIsIklucHV0IiwiSWNvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirst: () => (/* binding */ capitalizeFirst),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n// Utility function to merge Tailwind classes\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Format date utilities\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    }).format(date);\n}\nfunction formatRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return formatDate(date);\n}\n// Number formatting utilities\nfunction formatNumber(num) {\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n}\nfunction formatCurrency(amount, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: currency\n    }).format(amount);\n}\n// String utilities\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\nfunction capitalizeFirst(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\n// Status color mapping\nfunction getStatusColor(status) {\n    const statusColors = {\n        active: 'text-green-600 bg-green-50',\n        paused: 'text-yellow-600 bg-yellow-50',\n        closed: 'text-red-600 bg-red-50',\n        draft: 'text-gray-600 bg-gray-50',\n        new: 'text-blue-600 bg-blue-50',\n        screening: 'text-purple-600 bg-purple-50',\n        interview: 'text-orange-600 bg-orange-50',\n        offer: 'text-green-600 bg-green-50',\n        hired: 'text-green-700 bg-green-100',\n        rejected: 'text-red-600 bg-red-50'\n    };\n    return statusColors[status] || 'text-gray-600 bg-gray-50';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBNkM7QUFDSjtBQUV6Qyw2Q0FBNkM7QUFDdEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRUEsd0JBQXdCO0FBQ2pCLFNBQVNDLFdBQVdDLElBQVU7SUFDbkMsT0FBTyxJQUFJQyxLQUFLQyxjQUFjLENBQUMsU0FBUztRQUN0Q0MsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLEtBQUs7SUFDUCxHQUFHQyxNQUFNLENBQUNOO0FBQ1o7QUFFTyxTQUFTTyxtQkFBbUJQLElBQVU7SUFDM0MsTUFBTVEsTUFBTSxJQUFJQztJQUNoQixNQUFNQyxnQkFBZ0JDLEtBQUtDLEtBQUssQ0FBQyxDQUFDSixJQUFJSyxPQUFPLEtBQUtiLEtBQUthLE9BQU8sRUFBQyxJQUFLO0lBRXBFLElBQUlILGdCQUFnQixJQUFJLE9BQU87SUFDL0IsSUFBSUEsZ0JBQWdCLE1BQU0sT0FBTyxHQUFHQyxLQUFLQyxLQUFLLENBQUNGLGdCQUFnQixJQUFJLEtBQUssQ0FBQztJQUN6RSxJQUFJQSxnQkFBZ0IsT0FBTyxPQUFPLEdBQUdDLEtBQUtDLEtBQUssQ0FBQ0YsZ0JBQWdCLE1BQU0sS0FBSyxDQUFDO0lBQzVFLElBQUlBLGdCQUFnQixTQUFTLE9BQU8sR0FBR0MsS0FBS0MsS0FBSyxDQUFDRixnQkFBZ0IsT0FBTyxLQUFLLENBQUM7SUFFL0UsT0FBT1gsV0FBV0M7QUFDcEI7QUFFQSw4QkFBOEI7QUFDdkIsU0FBU2MsYUFBYUMsR0FBVztJQUN0QyxJQUFJQSxPQUFPLFNBQVM7UUFDbEIsT0FBTyxDQUFDQSxNQUFNLE9BQU0sRUFBR0MsT0FBTyxDQUFDLEtBQUs7SUFDdEM7SUFDQSxJQUFJRCxPQUFPLE1BQU07UUFDZixPQUFPLENBQUNBLE1BQU0sSUFBRyxFQUFHQyxPQUFPLENBQUMsS0FBSztJQUNuQztJQUNBLE9BQU9ELElBQUlFLFFBQVE7QUFDckI7QUFFTyxTQUFTQyxlQUFlQyxNQUFjLEVBQUVDLFdBQW1CLEtBQUs7SUFDckUsT0FBTyxJQUFJbkIsS0FBS29CLFlBQVksQ0FBQyxTQUFTO1FBQ3BDQyxPQUFPO1FBQ1BGLFVBQVVBO0lBQ1osR0FBR2QsTUFBTSxDQUFDYTtBQUNaO0FBRUEsbUJBQW1CO0FBQ1osU0FBU0ksYUFBYUMsSUFBWSxFQUFFQyxTQUFpQjtJQUMxRCxJQUFJRCxLQUFLRSxNQUFNLElBQUlELFdBQVcsT0FBT0Q7SUFDckMsT0FBT0EsS0FBS0csS0FBSyxDQUFDLEdBQUdGLGFBQWE7QUFDcEM7QUFFTyxTQUFTRyxnQkFBZ0JDLEdBQVc7SUFDekMsT0FBT0EsSUFBSUMsTUFBTSxDQUFDLEdBQUdDLFdBQVcsS0FBS0YsSUFBSUYsS0FBSyxDQUFDO0FBQ2pEO0FBRUEsdUJBQXVCO0FBQ2hCLFNBQVNLLGVBQWVDLE1BQWM7SUFDM0MsTUFBTUMsZUFBdUM7UUFDM0NDLFFBQVE7UUFDUkMsUUFBUTtRQUNSQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsS0FBSztRQUNMQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFVBQVU7SUFDWjtJQUVBLE9BQU9WLFlBQVksQ0FBQ0QsT0FBTyxJQUFJO0FBQ2pDIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXFdlYiBGaWxlc1xcbmVhcnRla3BvZFxcYWktcmVrcnVpdC1wcm9cXGFpLXJla3J1aXQtcHJvIG5leHRcXGFpLXJla3J1aXQtcHJvXFxzcmNcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIjtcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIjtcblxuLy8gVXRpbGl0eSBmdW5jdGlvbiB0byBtZXJnZSBUYWlsd2luZCBjbGFzc2VzXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcbn1cblxuLy8gRm9ybWF0IGRhdGUgdXRpbGl0aWVzXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RGF0ZShkYXRlOiBEYXRlKTogc3RyaW5nIHtcbiAgcmV0dXJuIG5ldyBJbnRsLkRhdGVUaW1lRm9ybWF0KCdlbi1VUycsIHtcbiAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgbW9udGg6ICdzaG9ydCcsXG4gICAgZGF5OiAnbnVtZXJpYydcbiAgfSkuZm9ybWF0KGRhdGUpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0UmVsYXRpdmVUaW1lKGRhdGU6IERhdGUpOiBzdHJpbmcge1xuICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICBjb25zdCBkaWZmSW5TZWNvbmRzID0gTWF0aC5mbG9vcigobm93LmdldFRpbWUoKSAtIGRhdGUuZ2V0VGltZSgpKSAvIDEwMDApO1xuICBcbiAgaWYgKGRpZmZJblNlY29uZHMgPCA2MCkgcmV0dXJuICdKdXN0IG5vdyc7XG4gIGlmIChkaWZmSW5TZWNvbmRzIDwgMzYwMCkgcmV0dXJuIGAke01hdGguZmxvb3IoZGlmZkluU2Vjb25kcyAvIDYwKX1tIGFnb2A7XG4gIGlmIChkaWZmSW5TZWNvbmRzIDwgODY0MDApIHJldHVybiBgJHtNYXRoLmZsb29yKGRpZmZJblNlY29uZHMgLyAzNjAwKX1oIGFnb2A7XG4gIGlmIChkaWZmSW5TZWNvbmRzIDwgMjU5MjAwMCkgcmV0dXJuIGAke01hdGguZmxvb3IoZGlmZkluU2Vjb25kcyAvIDg2NDAwKX1kIGFnb2A7XG4gIFxuICByZXR1cm4gZm9ybWF0RGF0ZShkYXRlKTtcbn1cblxuLy8gTnVtYmVyIGZvcm1hdHRpbmcgdXRpbGl0aWVzXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0TnVtYmVyKG51bTogbnVtYmVyKTogc3RyaW5nIHtcbiAgaWYgKG51bSA+PSAxMDAwMDAwKSB7XG4gICAgcmV0dXJuIChudW0gLyAxMDAwMDAwKS50b0ZpeGVkKDEpICsgJ00nO1xuICB9XG4gIGlmIChudW0gPj0gMTAwMCkge1xuICAgIHJldHVybiAobnVtIC8gMTAwMCkudG9GaXhlZCgxKSArICdLJztcbiAgfVxuICByZXR1cm4gbnVtLnRvU3RyaW5nKCk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRDdXJyZW5jeShhbW91bnQ6IG51bWJlciwgY3VycmVuY3k6IHN0cmluZyA9ICdVU0QnKTogc3RyaW5nIHtcbiAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZW4tVVMnLCB7XG4gICAgc3R5bGU6ICdjdXJyZW5jeScsXG4gICAgY3VycmVuY3k6IGN1cnJlbmN5LFxuICB9KS5mb3JtYXQoYW1vdW50KTtcbn1cblxuLy8gU3RyaW5nIHV0aWxpdGllc1xuZXhwb3J0IGZ1bmN0aW9uIHRydW5jYXRlVGV4dCh0ZXh0OiBzdHJpbmcsIG1heExlbmd0aDogbnVtYmVyKTogc3RyaW5nIHtcbiAgaWYgKHRleHQubGVuZ3RoIDw9IG1heExlbmd0aCkgcmV0dXJuIHRleHQ7XG4gIHJldHVybiB0ZXh0LnNsaWNlKDAsIG1heExlbmd0aCkgKyAnLi4uJztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNhcGl0YWxpemVGaXJzdChzdHI6IHN0cmluZyk6IHN0cmluZyB7XG4gIHJldHVybiBzdHIuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBzdHIuc2xpY2UoMSk7XG59XG5cbi8vIFN0YXR1cyBjb2xvciBtYXBwaW5nXG5leHBvcnQgZnVuY3Rpb24gZ2V0U3RhdHVzQ29sb3Ioc3RhdHVzOiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBzdGF0dXNDb2xvcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgYWN0aXZlOiAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tNTAnLFxuICAgIHBhdXNlZDogJ3RleHQteWVsbG93LTYwMCBiZy15ZWxsb3ctNTAnLFxuICAgIGNsb3NlZDogJ3RleHQtcmVkLTYwMCBiZy1yZWQtNTAnLFxuICAgIGRyYWZ0OiAndGV4dC1ncmF5LTYwMCBiZy1ncmF5LTUwJyxcbiAgICBuZXc6ICd0ZXh0LWJsdWUtNjAwIGJnLWJsdWUtNTAnLFxuICAgIHNjcmVlbmluZzogJ3RleHQtcHVycGxlLTYwMCBiZy1wdXJwbGUtNTAnLFxuICAgIGludGVydmlldzogJ3RleHQtb3JhbmdlLTYwMCBiZy1vcmFuZ2UtNTAnLFxuICAgIG9mZmVyOiAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tNTAnLFxuICAgIGhpcmVkOiAndGV4dC1ncmVlbi03MDAgYmctZ3JlZW4tMTAwJyxcbiAgICByZWplY3RlZDogJ3RleHQtcmVkLTYwMCBiZy1yZWQtNTAnLFxuICB9O1xuICBcbiAgcmV0dXJuIHN0YXR1c0NvbG9yc1tzdGF0dXNdIHx8ICd0ZXh0LWdyYXktNjAwIGJnLWdyYXktNTAnO1xufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJmb3JtYXREYXRlIiwiZGF0ZSIsIkludGwiLCJEYXRlVGltZUZvcm1hdCIsInllYXIiLCJtb250aCIsImRheSIsImZvcm1hdCIsImZvcm1hdFJlbGF0aXZlVGltZSIsIm5vdyIsIkRhdGUiLCJkaWZmSW5TZWNvbmRzIiwiTWF0aCIsImZsb29yIiwiZ2V0VGltZSIsImZvcm1hdE51bWJlciIsIm51bSIsInRvRml4ZWQiLCJ0b1N0cmluZyIsImZvcm1hdEN1cnJlbmN5IiwiYW1vdW50IiwiY3VycmVuY3kiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsInRydW5jYXRlVGV4dCIsInRleHQiLCJtYXhMZW5ndGgiLCJsZW5ndGgiLCJzbGljZSIsImNhcGl0YWxpemVGaXJzdCIsInN0ciIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwiZ2V0U3RhdHVzQ29sb3IiLCJzdGF0dXMiLCJzdGF0dXNDb2xvcnMiLCJhY3RpdmUiLCJwYXVzZWQiLCJjbG9zZWQiLCJkcmFmdCIsIm5ldyIsInNjcmVlbmluZyIsImludGVydmlldyIsIm9mZmVyIiwiaGlyZWQiLCJyZWplY3RlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// Core data types for the recruitment application\n// Search and filter interfaces\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/index.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fjobs%2Fpage&page=%2F(dashboard)%2Fjobs%2Fpage&appPaths=%2F(dashboard)%2Fjobs%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fjobs%2Fpage.tsx&appDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cai-rekruit-pro%5Cai-rekruit-pro%20next%5Cai-rekruit-pro%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cai-rekruit-pro%5Cai-rekruit-pro%20next%5Cai-rekruit-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();