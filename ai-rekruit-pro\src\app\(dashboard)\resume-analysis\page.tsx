'use client';

import React from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  Icon,
  Badge,
  DataTable
} from '@/components';

export default function ResumeAnalysisPage() {
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = React.useState(false);
  const [analysisResults, setAnalysisResults] = React.useState<any>(null);

  // Sample analysis history
  const analysisHistory = [
    {
      id: '1',
      fileName: 'john_doe_resume.pdf',
      candidateName: '<PERSON>',
      score: 85,
      matchedSkills: ['React', 'TypeScript', 'Node.js'],
      analyzedDate: '2024-01-20',
      status: 'completed'
    },
    {
      id: '2',
      fileName: 'jane_smith_resume.pdf',
      candidateName: '<PERSON>',
      score: 92,
      matchedSkills: ['Python', 'Django', 'PostgreSQL'],
      analyzedDate: '2024-01-19',
      status: 'completed'
    },
    {
      id: '3',
      fileName: 'mike_johnson_resume.pdf',
      candidateName: '<PERSON>',
      score: 78,
      matchedSkills: ['JavaScript', 'Vue.js', 'MongoDB'],
      analyzedDate: '2024-01-18',
      status: 'completed'
    }
  ];

  const tableColumns = [
    { key: 'candidateName', title: 'Candidate', sortable: true },
    { key: 'fileName', title: 'File Name', sortable: true },
    { 
      key: 'score', 
      title: 'Match Score', 
      sortable: true,
      render: (value: number) => (
        <div className="flex items-center">
          <span className={`font-semibold ${
            value >= 90 ? 'text-green-600' : 
            value >= 75 ? 'text-yellow-600' : 
            'text-red-600'
          }`}>
            {value}%
          </span>
        </div>
      )
    },
    { 
      key: 'matchedSkills', 
      title: 'Top Skills',
      render: (skills: string[]) => (
        <div className="flex flex-wrap gap-1">
          {skills.slice(0, 3).map((skill, index) => (
            <Badge key={index} variant="info" size="sm">
              {skill}
            </Badge>
          ))}
          {skills.length > 3 && (
            <Badge variant="default" size="sm">
              +{skills.length - 3}
            </Badge>
          )}
        </div>
      )
    },
    { key: 'analyzedDate', title: 'Analyzed Date', sortable: true },
    { 
      key: 'status', 
      title: 'Status',
      render: (value: string) => (
        <Badge variant={value === 'completed' ? 'success' : 'warning'}>
          {value}
        </Badge>
      )
    }
  ];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleAnalyze = async () => {
    if (!selectedFile) return;
    
    setIsAnalyzing(true);
    
    // Simulate AI analysis
    setTimeout(() => {
      setAnalysisResults({
        score: 87,
        skills: ['React', 'TypeScript', 'Node.js', 'AWS', 'Docker'],
        experience: '5+ years',
        education: 'Bachelor\'s in Computer Science',
        recommendations: [
          'Strong technical background in modern web technologies',
          'Good match for Senior Frontend Developer position',
          'Consider for technical interview round'
        ],
        weaknesses: [
          'Limited experience with mobile development',
          'No mention of testing frameworks'
        ]
      });
      setIsAnalyzing(false);
    }, 3000);
  };

  const handleBulkAnalysis = () => {
    console.log('Start bulk analysis');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Resume Analysis</h1>
          <p className="text-gray-600">AI-powered resume screening and analysis</p>
        </div>
        <Button variant="outline" onClick={handleBulkAnalysis}>
          Bulk Analysis
        </Button>
      </div>

      {/* Upload Section */}
      <Card title="Upload Resume for Analysis" padding="lg">
        <div className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
            <Icon name="upload" size="xl" className="mx-auto text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-lg font-medium text-gray-900">
                Drop your resume here or click to browse
              </p>
              <p className="text-sm text-gray-500">
                Supports PDF, DOC, DOCX files up to 10MB
              </p>
            </div>
            <input
              type="file"
              accept=".pdf,.doc,.docx"
              onChange={handleFileSelect}
              className="hidden"
              id="resume-upload"
            />
            <label
              htmlFor="resume-upload"
              className="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
            >
              Choose File
            </label>
          </div>

          {selectedFile && (
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Icon name="file" size="md" className="text-blue-600" />
                <div>
                  <p className="font-medium text-gray-900">{selectedFile.name}</p>
                  <p className="text-sm text-gray-500">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <Button 
                variant="primary" 
                onClick={handleAnalyze}
                loading={isAnalyzing}
                disabled={isAnalyzing}
              >
                {isAnalyzing ? 'Analyzing...' : 'Analyze Resume'}
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* Analysis Results */}
      {analysisResults && (
        <Card title="Analysis Results" padding="lg">
          <div className="space-y-6">
            {/* Score */}
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-24 h-24 bg-green-100 rounded-full mb-4">
                <span className="text-3xl font-bold text-green-600">
                  {analysisResults.score}%
                </span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Match Score</h3>
              <p className="text-gray-600">Strong candidate for the position</p>
            </div>

            {/* Skills */}
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Identified Skills</h4>
              <div className="flex flex-wrap gap-2">
                {analysisResults.skills.map((skill: string, index: number) => (
                  <Badge key={index} variant="info">
                    {skill}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Recommendations */}
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Recommendations</h4>
              <ul className="space-y-2">
                {analysisResults.recommendations.map((rec: string, index: number) => (
                  <li key={index} className="flex items-start space-x-2">
                    <Icon name="check" size="sm" className="text-green-500 mt-0.5" />
                    <span className="text-gray-700">{rec}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </Card>
      )}

      {/* Analysis History */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Analysis History</h2>
        <DataTable
          columns={tableColumns}
          data={analysisHistory}
          onSort={(key, direction) => console.log('Sort:', key, direction)}
          onRowClick={(row) => console.log('Row clicked:', row)}
        />
      </div>
    </div>
  );
}
