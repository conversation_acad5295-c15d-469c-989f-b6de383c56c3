"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/candidates/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/candidates/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/(dashboard)/candidates/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CandidatesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components */ \"(app-pages-browser)/./src/components/index.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CandidatesPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('');\n    const [statusFilters, setStatusFilters] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const [skillFilters, setSkillFilters] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    // Sample candidate data\n    const candidates = [\n        {\n            id: '1',\n            name: 'Sarah Johnson',\n            email: '<EMAIL>',\n            phone: '+****************',\n            status: 'screening',\n            appliedJobs: [\n                '1'\n            ],\n            skills: [\n                'React',\n                'TypeScript',\n                'Node.js',\n                'Python'\n            ],\n            experience: 5,\n            location: 'San Francisco, CA',\n            appliedDate: new Date('2024-01-20')\n        },\n        {\n            id: '2',\n            name: 'Mike Chen',\n            email: '<EMAIL>',\n            phone: '+****************',\n            status: 'interview',\n            appliedJobs: [\n                '1',\n                '3'\n            ],\n            skills: [\n                'JavaScript',\n                'React',\n                'AWS',\n                'Docker'\n            ],\n            experience: 7,\n            location: 'Seattle, WA',\n            appliedDate: new Date('2024-01-18')\n        },\n        {\n            id: '3',\n            name: 'Emily Rodriguez',\n            email: '<EMAIL>',\n            phone: '+****************',\n            status: 'offer',\n            appliedJobs: [\n                '2'\n            ],\n            skills: [\n                'Figma',\n                'Adobe XD',\n                'User Research',\n                'Prototyping'\n            ],\n            experience: 4,\n            location: 'Austin, TX',\n            appliedDate: new Date('2024-01-15')\n        },\n        {\n            id: '4',\n            name: 'David Kim',\n            email: '<EMAIL>',\n            status: 'hired',\n            appliedJobs: [\n                '3'\n            ],\n            skills: [\n                'Python',\n                'Django',\n                'PostgreSQL',\n                'Redis'\n            ],\n            experience: 6,\n            location: 'New York, NY',\n            appliedDate: new Date('2024-01-10')\n        }\n    ];\n    const statusOptions = [\n        {\n            value: 'new',\n            label: 'New',\n            count: 0\n        },\n        {\n            value: 'screening',\n            label: 'Screening',\n            count: 1\n        },\n        {\n            value: 'interview',\n            label: 'Interview',\n            count: 1\n        },\n        {\n            value: 'offer',\n            label: 'Offer',\n            count: 1\n        },\n        {\n            value: 'hired',\n            label: 'Hired',\n            count: 1\n        },\n        {\n            value: 'rejected',\n            label: 'Rejected',\n            count: 0\n        }\n    ];\n    const skillOptions = [\n        {\n            value: 'react',\n            label: 'React',\n            count: 2\n        },\n        {\n            value: 'typescript',\n            label: 'TypeScript',\n            count: 1\n        },\n        {\n            value: 'python',\n            label: 'Python',\n            count: 2\n        },\n        {\n            value: 'figma',\n            label: 'Figma',\n            count: 1\n        },\n        {\n            value: 'aws',\n            label: 'AWS',\n            count: 1\n        }\n    ];\n    const tableColumns = [\n        {\n            key: 'name',\n            title: 'Name',\n            sortable: true\n        },\n        {\n            key: 'email',\n            title: 'Email',\n            sortable: true\n        },\n        {\n            key: 'location',\n            title: 'Location',\n            sortable: true\n        },\n        {\n            key: 'experience',\n            title: 'Experience',\n            sortable: true\n        },\n        {\n            key: 'status',\n            title: 'Status',\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                    variant: value === 'hired' ? 'success' : value === 'offer' ? 'success' : value === 'interview' ? 'warning' : 'default',\n                    children: value\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: 'appliedDate',\n            title: 'Applied Date',\n            sortable: true\n        }\n    ];\n    const tableData = candidates.map((candidate)=>({\n            ...candidate,\n            experience: \"\".concat(candidate.experience, \" years\"),\n            appliedDate: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateConsistent)(candidate.appliedDate)\n        }));\n    const handleCandidateAction = (action, candidate)=>{\n        console.log(\"\".concat(action, \" candidate:\"), candidate);\n    };\n    const handleBulkUpload = ()=>{\n        console.log('Bulk upload resumes');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl sm:text-2xl font-bold text-gray-900\",\n                                children: \"Candidates\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage and review candidate applications\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkUpload,\n                                className: \"w-full sm:w-auto\",\n                                children: \"Bulk Upload\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"primary\",\n                                className: \"w-full sm:w-auto\",\n                                children: \"Add Candidate\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                padding: \"md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.SearchBar, {\n                            placeholder: \"Search candidates by name, email, or skills...\",\n                            value: searchQuery,\n                            onChange: setSearchQuery,\n                            onSearch: (query)=>console.log('Search:', query)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FilterDropdown, {\n                                    title: \"Status\",\n                                    options: statusOptions,\n                                    selectedValues: statusFilters,\n                                    onChange: setStatusFilters,\n                                    placeholder: \"Filter by status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.FilterDropdown, {\n                                    title: \"Skills\",\n                                    options: skillOptions,\n                                    selectedValues: skillFilters,\n                                    onChange: setSkillFilters,\n                                    placeholder: \"Filter by skills\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Recent Applications\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: candidates.slice(0, 3).map((candidate)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.CandidateCard, {\n                                candidate: candidate,\n                                onView: (candidate)=>handleCandidateAction('view', candidate),\n                                onContact: (candidate)=>handleCandidateAction('contact', candidate),\n                                onScheduleInterview: (candidate)=>handleCandidateAction('schedule', candidate)\n                            }, candidate.id, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"All Candidates\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                        columns: tableColumns,\n                        data: tableData,\n                        onSort: (key, direction)=>console.log('Sort:', key, direction),\n                        onRowClick: (row)=>console.log('Row clicked:', row)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\ai-rekruit-pro\\\\ai-rekruit-pro next\\\\ai-rekruit-pro\\\\src\\\\app\\\\(dashboard)\\\\candidates\\\\page.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(CandidatesPage, \"CbqPk5TRgdqZ6snlQjcu+Fv8lqs=\");\n_c = CandidatesPage;\nvar _c;\n$RefreshReg$(_c, \"CandidatesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/candidates/page.tsx\n"));

/***/ })

});