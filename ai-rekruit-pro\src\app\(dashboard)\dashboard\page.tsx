'use client';

import React from 'react';
import { MetricCard, Card } from '@/components';
import { DashboardMetric } from '@/types';

export default function DashboardPage() {
  const metrics: DashboardMetric[] = [
    {
      id: '1',
      title: 'Active Jobs',
      value: '5',
      change: 25,
      changeType: 'increase',
      icon: 'jobs',
      color: 'blue'
    }
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => (
          <MetricCard key={metric.id} metric={metric} />
        ))}
      </div>

      <Card title="Welcome" padding="lg">
        <p>Welcome to AI RecruitPro Dashboard!</p>
      </Card>
    </div>
  );
}
