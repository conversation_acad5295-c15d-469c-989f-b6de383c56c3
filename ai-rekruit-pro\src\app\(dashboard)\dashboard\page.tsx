'use client';

import React from 'react';
import {
  MetricCard,
  JobPostingCard,
  CandidateCard,
  QuickAction,
  DataTable,
  Badge,
  Button,
  Card
} from '@/components';
import { DashboardMetric, Job, Candidate, QuickAction as QuickActionType } from '@/types';

export default function DashboardPage() {
  // Key recruitment metrics with trend indicators
  const metrics: DashboardMetric[] = [
    {
      id: '1',
      title: 'Active Jobs',
      value: '12',
      change: 25,
      changeType: 'increase',
      icon: 'jobs',
      color: 'blue'
    },
    {
      id: '2',
      title: 'Total Candidates',
      value: '247',
      change: 18,
      changeType: 'increase',
      icon: 'candidates',
      color: 'green'
    },
    {
      id: '3',
      title: 'Hired This Month',
      value: '8',
      change: -12,
      changeType: 'decrease',
      icon: 'analytics',
      color: 'orange'
    },
    {
      id: '4',
      title: 'Pending Reviews',
      value: '23',
      change: 5,
      changeType: 'increase',
      icon: 'eye',
      color: 'purple'
    }
  ];

  // Quick action cards for common tasks
  const quickActions: QuickActionType[] = [
    {
      id: '1',
      title: 'Create Job Posting',
      description: 'Post a new job opening and start attracting candidates',
      icon: 'plus',
      action: () => {
        console.log('Navigate to create job posting');
        // In a real app, this would navigate to the job creation page
      },
      color: 'blue'
    },
    {
      id: '2',
      title: 'Bulk Resume Upload',
      description: 'Upload multiple resumes for AI-powered analysis',
      icon: 'upload',
      action: () => {
        console.log('Open bulk resume upload modal');
        // In a real app, this would open a file upload modal
      },
      color: 'green'
    },
    {
      id: '3',
      title: 'Generate Report',
      description: 'Create comprehensive recruitment analytics report',
      icon: 'download',
      action: () => {
        console.log('Generate and download recruitment report');
        // In a real app, this would generate a PDF report
      },
      color: 'purple'
    },
    {
      id: '4',
      title: 'Schedule Interviews',
      description: 'Manage and schedule candidate interviews',
      icon: 'calendar',
      action: () => {
        console.log('Navigate to interview scheduling');
        // In a real app, this would navigate to the calendar page
      },
      color: 'orange'
    }
  ];

  // Sample recent job postings
  const recentJobs: Job[] = [
    {
      id: '1',
      title: 'Senior Frontend Developer',
      department: 'Engineering',
      location: 'San Francisco, CA',
      type: 'full-time',
      status: 'active',
      description: 'We are looking for an experienced frontend developer to join our team and help build amazing user experiences with React and TypeScript.',
      requirements: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS'],
      postedDate: new Date('2024-01-15'),
      closingDate: new Date('2024-02-15'),
      salary: { min: 120000, max: 160000, currency: 'USD' }
    },
    {
      id: '2',
      title: 'UX/UI Designer',
      department: 'Design',
      location: 'Remote',
      type: 'full-time',
      status: 'active',
      description: 'Join our design team to create intuitive and beautiful user interfaces that delight our customers.',
      requirements: ['Figma', 'Adobe Creative Suite', 'User Research', 'Prototyping'],
      postedDate: new Date('2024-01-10'),
      closingDate: new Date('2024-02-10'),
      salary: { min: 90000, max: 120000, currency: 'USD' }
    },
    {
      id: '3',
      title: 'Backend Engineer',
      department: 'Engineering',
      location: 'New York, NY',
      type: 'full-time',
      status: 'paused',
      description: 'Build scalable backend systems and APIs for our growing platform using modern technologies.',
      requirements: ['Node.js', 'Python', 'PostgreSQL', 'AWS', 'Docker'],
      postedDate: new Date('2024-01-20'),
      closingDate: new Date('2024-02-20'),
      salary: { min: 130000, max: 170000, currency: 'USD' }
    }
  ];

  // Top matched candidates
  const topCandidates: Candidate[] = [
    {
      id: '1',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'screening',
      appliedJobs: ['1'],
      skills: ['React', 'TypeScript', 'Node.js', 'Python', 'AWS'],
      experience: 5,
      location: 'San Francisco, CA',
      appliedDate: new Date('2024-01-20')
    },
    {
      id: '2',
      name: 'Michael Chen',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'interview',
      appliedJobs: ['1', '3'],
      skills: ['JavaScript', 'React', 'AWS', 'Docker', 'Kubernetes'],
      experience: 7,
      location: 'Seattle, WA',
      appliedDate: new Date('2024-01-18')
    },
    {
      id: '3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'offer',
      appliedJobs: ['2'],
      skills: ['Figma', 'Adobe XD', 'User Research', 'Prototyping', 'Design Systems'],
      experience: 4,
      location: 'Austin, TX',
      appliedDate: new Date('2024-01-15')
    }
  ];

  // Data table configuration for candidates overview
  const tableColumns = [
    { key: 'name', title: 'Name', sortable: true },
    { key: 'email', title: 'Email', sortable: true },
    { key: 'location', title: 'Location', sortable: true },
    { key: 'experience', title: 'Experience', sortable: true },
    {
      key: 'status',
      title: 'Status',
      render: (value: string) => {
        const getVariant = (status: string) => {
          switch (status) {
            case 'hired': return 'success';
            case 'offer': return 'success';
            case 'interview': return 'warning';
            case 'screening': return 'info';
            case 'rejected': return 'danger';
            default: return 'default';
          }
        };
        return React.createElement(Badge, {
          variant: getVariant(value),
          size: 'sm'
        }, value);
      }
    },
    { key: 'appliedDate', title: 'Applied Date', sortable: true }
  ];

  // Extended candidate data for table
  const tableData = [
    ...topCandidates.map(candidate => ({
      ...candidate,
      experience: `${candidate.experience} years`,
      appliedDate: candidate.appliedDate.toLocaleDateString()
    })),
    {
      id: '4',
      name: 'David Kim',
      email: '<EMAIL>',
      location: 'New York, NY',
      experience: '6 years',
      status: 'hired',
      appliedDate: '2024-01-10'
    },
    {
      id: '5',
      name: 'Lisa Wang',
      email: '<EMAIL>',
      location: 'Los Angeles, CA',
      experience: '3 years',
      status: 'rejected',
      appliedDate: '2024-01-12'
    }
  ];

  // Event handlers for interactive features
  const handleMetricClick = (metric: DashboardMetric) => {
    console.log('Metric clicked:', metric.title);
    // In a real app, this could navigate to detailed analytics
  };

  const handleJobAction = (action: string, job: Job) => {
    console.log(`${action} job:`, job.title);
    // In a real app, this would handle job-specific actions
  };

  const handleCandidateAction = (action: string, candidate: Candidate) => {
    console.log(`${action} candidate:`, candidate.name);
    // In a real app, this would handle candidate-specific actions
  };

  const handleTableSort = (key: string, direction: 'asc' | 'desc') => {
    console.log('Sort table by:', key, direction);
    // In a real app, this would sort the table data
  };

  const handleTableRowClick = (row: any) => {
    console.log('Table row clicked:', row.name);
    // In a real app, this would navigate to candidate details
  };

  return (
    <div className="space-y-6">
      {/* Dashboard Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
          <p className="mt-1 text-sm text-gray-600">
            Welcome back! Here's what's happening with your recruitment process.
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button
            variant="primary"
            onClick={() => console.log('Navigate to create job')}
            className="w-full sm:w-auto"
          >
            Post New Job
          </Button>
        </div>
      </div>

      {/* Key Metrics Section */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Key Metrics</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {metrics.map((metric) => (
            <div
              key={metric.id}
              onClick={() => handleMetricClick(metric)}
              className="cursor-pointer transform transition-transform hover:scale-105"
            >
              <MetricCard metric={metric} />
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions Panel */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickActions.map((action) => (
            <QuickAction key={action.id} action={action} />
          ))}
        </div>
      </div>

      {/* Recent Job Postings Section */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Recent Job Postings</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => console.log('Navigate to all jobs')}
          >
            View All Jobs
          </Button>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {recentJobs.slice(0, 3).map((job) => (
            <JobPostingCard
              key={job.id}
              job={job}
              onView={(job) => handleJobAction('view', job)}
              onEdit={(job) => handleJobAction('edit', job)}
              onDelete={(job) => handleJobAction('delete', job)}
            />
          ))}
        </div>
      </div>

      {/* Top Matched Candidates Section */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Top Matched Candidates</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => console.log('Navigate to all candidates')}
          >
            View All Candidates
          </Button>
        </div>
        <div className="space-y-4">
          {topCandidates.map((candidate) => (
            <CandidateCard
              key={candidate.id}
              candidate={candidate}
              onView={(candidate) => handleCandidateAction('view', candidate)}
              onContact={(candidate) => handleCandidateAction('contact', candidate)}
              onScheduleInterview={(candidate) => handleCandidateAction('schedule_interview', candidate)}
            />
          ))}
        </div>
      </div>

      {/* Candidates Overview Table */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Candidates Overview</h2>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => console.log('Export candidates data')}
            >
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => console.log('Filter candidates')}
            >
              Filter
            </Button>
          </div>
        </div>
        <Card padding="none">
          <DataTable
            columns={tableColumns}
            data={tableData}
            onSort={handleTableSort}
            onRowClick={handleTableRowClick}
            emptyMessage="No candidates found"
          />
        </Card>
      </div>

      {/* Recent Activity & Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card title="Recent Activity" padding="lg">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">New application received</p>
                <p className="text-xs text-gray-500">Sarah Johnson applied for Senior Frontend Developer</p>
                <p className="text-xs text-gray-400">2 hours ago</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Interview scheduled</p>
                <p className="text-xs text-gray-500">Michael Chen - Technical Interview on Jan 25</p>
                <p className="text-xs text-gray-400">4 hours ago</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Offer extended</p>
                <p className="text-xs text-gray-500">Emily Rodriguez - UX Designer position</p>
                <p className="text-xs text-gray-400">1 day ago</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Key Insights */}
        <Card title="Key Insights" padding="lg">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-sm font-semibold">↗</span>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Application Rate Up</p>
                <p className="text-xs text-gray-600">25% increase in applications this month</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-sm font-semibold">⚡</span>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Faster Screening</p>
                <p className="text-xs text-gray-600">AI analysis reduced screening time by 40%</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <span className="text-orange-600 text-sm font-semibold">🎯</span>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Quality Matches</p>
                <p className="text-xs text-gray-600">Higher candidate-job match scores this week</p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
