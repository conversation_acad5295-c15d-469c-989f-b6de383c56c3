'use client';

import React from 'react';
import {
  MetricCard,
  JobPostingCard,
  CandidateCard,
  QuickAction,
  Button,
  SearchBar,
  FilterDropdown,
  DataTable,
  Badge
} from '@/components';
import { DashboardMetric, Job, Candidate, QuickAction as QuickActionType } from '@/types';

export default function DashboardPage() {
  // Sample data for demonstration
  const metrics: DashboardMetric[] = [
    {
      id: '1',
      title: 'Active Jobs',
      value: '5',
      change: 25,
      changeType: 'increase',
      icon: 'jobs',
      color: 'blue'
    },
    {
      id: '2',
      title: 'Total Candidates',
      value: '42',
      change: 12,
      changeType: 'increase',
      icon: 'candidates',
      color: 'green'
    },
    {
      id: '3',
      title: 'Hired This Month',
      value: '3',
      change: -8,
      changeType: 'decrease',
      icon: 'analytics',
      color: 'orange'
    },
    {
      id: '4',
      title: 'Pending Reviews',
      value: '12',
      icon: 'eye',
      color: 'purple'
    }
  ];

  const sampleJob: Job = {
    id: '1',
    title: 'Senior Frontend Developer',
    department: 'Engineering',
    location: 'San Francisco, CA',
    type: 'full-time',
    status: 'active',
    description: 'We are looking for an experienced frontend developer to join our team...',
    requirements: ['React', 'TypeScript', 'Next.js'],
    postedDate: new Date('2024-01-15'),
    closingDate: new Date('2024-02-15'),
    salary: {
      min: 120000,
      max: 160000,
      currency: 'USD'
    }
  };

  const sampleCandidate: Candidate = {
    id: '1',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'screening',
    appliedJobs: ['1'],
    skills: ['React', 'TypeScript', 'Node.js', 'Python'],
    experience: 5,
    location: 'San Francisco, CA',
    appliedDate: new Date('2024-01-20')
  };

  const quickActions: QuickActionType[] = [
    {
      id: '1',
      title: 'Create Job Posting',
      description: 'Post a new job opening',
      icon: 'plus',
      action: () => console.log('Create job'),
      color: 'blue'
    },
    {
      id: '2',
      title: 'Bulk Resume Upload',
      description: 'Upload multiple resumes',
      icon: 'upload',
      action: () => console.log('Upload resumes'),
      color: 'green'
    },
    {
      id: '3',
      title: 'Generate Report',
      description: 'Create hiring analytics report',
      icon: 'download',
      action: () => console.log('Generate report'),
      color: 'purple'
    }
  ];

  const tableColumns = [
    { key: 'name', title: 'Name', sortable: true },
    { key: 'email', title: 'Email', sortable: true },
    { 
      key: 'status', 
      title: 'Status', 
      render: (value: string) => (
        <Badge variant={value === 'hired' ? 'success' : 'default'}>
          {value}
        </Badge>
      )
    },
    { key: 'appliedDate', title: 'Applied Date', sortable: true }
  ];

  const tableData = [
    { name: 'John Doe', email: '<EMAIL>', status: 'screening', appliedDate: '2024-01-20' },
    { name: 'Jane Smith', email: '<EMAIL>', status: 'hired', appliedDate: '2024-01-18' },
  ];

  return (
    <div className="space-y-6">
      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => (
          <MetricCard key={metric.id} metric={metric} />
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {quickActions.map((action) => (
            <QuickAction key={action.id} action={action} />
          ))}
        </div>
      </div>

        {/* Recent Job Postings */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Job Postings</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <JobPostingCard 
              job={sampleJob}
              onView={(job) => console.log('View job:', job)}
              onEdit={(job) => console.log('Edit job:', job)}
              onDelete={(job) => console.log('Delete job:', job)}
            />
            <JobPostingCard 
              job={{...sampleJob, id: '2', title: 'UX Designer', status: 'paused'}}
              onView={(job) => console.log('View job:', job)}
              onEdit={(job) => console.log('Edit job:', job)}
              onDelete={(job) => console.log('Delete job:', job)}
            />
          </div>
        </div>

        {/* Top Matched Candidates */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Top Matched Candidates</h2>
          <div className="space-y-4">
            <CandidateCard 
              candidate={sampleCandidate}
              onView={(candidate) => console.log('View candidate:', candidate)}
              onContact={(candidate) => console.log('Contact candidate:', candidate)}
              onScheduleInterview={(candidate) => console.log('Schedule interview:', candidate)}
            />
            <CandidateCard 
              candidate={{...sampleCandidate, id: '2', name: 'Mike Chen', status: 'interview'}}
              onView={(candidate) => console.log('View candidate:', candidate)}
              onContact={(candidate) => console.log('Contact candidate:', candidate)}
              onScheduleInterview={(candidate) => console.log('Schedule interview:', candidate)}
            />
          </div>
        </div>

        {/* Data Table Example */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Candidates Table</h2>
          <DataTable 
            columns={tableColumns}
            data={tableData}
            onSort={(key, direction) => console.log('Sort:', key, direction)}
            onRowClick={(row) => console.log('Row clicked:', row)}
          />
        </div>
      </div>
    </div>
  );
}
