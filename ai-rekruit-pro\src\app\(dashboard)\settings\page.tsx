'use client';

import React from 'react';
import { 
  Card, 
  Button,
  FormField,
  Badge,
  Icon
} from '@/components';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = React.useState('general');
  
  const [generalSettings, setGeneralSettings] = React.useState({
    companyName: 'TechCorp Inc.',
    companyWebsite: 'https://techcorp.com',
    companySize: '100-500',
    industry: 'Technology',
    defaultCurrency: 'USD',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12-hour'
  });

  const [integrationSettings, setIntegrationSettings] = React.useState({
    linkedinEnabled: true,
    indeedEnabled: true,
    glassdoorEnabled: false,
    slackEnabled: true,
    emailProvider: 'gmail'
  });

  const tabs = [
    { id: 'general', label: 'General', icon: 'settings' },
    { id: 'integrations', label: 'Integrations', icon: 'link' },
    { id: 'notifications', label: 'Notifications', icon: 'bell' },
    { id: 'security', label: 'Security', icon: 'shield' },
    { id: 'billing', label: 'Billing', icon: 'credit-card' }
  ];

  const handleGeneralChange = (name: string, value: string) => {
    setGeneralSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveSettings = () => {
    console.log('Save settings');
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <Card title="Company Information" padding="lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            label="Company Name"
            name="companyName"
            value={generalSettings.companyName}
            onChange={handleGeneralChange}
            required
          />
          <FormField
            label="Website"
            name="companyWebsite"
            type="url"
            value={generalSettings.companyWebsite}
            onChange={handleGeneralChange}
          />
          <FormField
            label="Company Size"
            name="companySize"
            type="select"
            value={generalSettings.companySize}
            onChange={handleGeneralChange}
            options={[
              { value: '1-10', label: '1-10 employees' },
              { value: '11-50', label: '11-50 employees' },
              { value: '51-100', label: '51-100 employees' },
              { value: '100-500', label: '100-500 employees' },
              { value: '500+', label: '500+ employees' }
            ]}
          />
          <FormField
            label="Industry"
            name="industry"
            type="select"
            value={generalSettings.industry}
            onChange={handleGeneralChange}
            options={[
              { value: 'Technology', label: 'Technology' },
              { value: 'Healthcare', label: 'Healthcare' },
              { value: 'Finance', label: 'Finance' },
              { value: 'Education', label: 'Education' },
              { value: 'Retail', label: 'Retail' }
            ]}
          />
        </div>
      </Card>

      <Card title="Regional Settings" padding="lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <FormField
            label="Default Currency"
            name="defaultCurrency"
            type="select"
            value={generalSettings.defaultCurrency}
            onChange={handleGeneralChange}
            options={[
              { value: 'USD', label: 'USD ($)' },
              { value: 'EUR', label: 'EUR (€)' },
              { value: 'GBP', label: 'GBP (£)' },
              { value: 'CAD', label: 'CAD (C$)' }
            ]}
          />
          <FormField
            label="Date Format"
            name="dateFormat"
            type="select"
            value={generalSettings.dateFormat}
            onChange={handleGeneralChange}
            options={[
              { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
              { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
              { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' }
            ]}
          />
          <FormField
            label="Time Format"
            name="timeFormat"
            type="select"
            value={generalSettings.timeFormat}
            onChange={handleGeneralChange}
            options={[
              { value: '12-hour', label: '12-hour (AM/PM)' },
              { value: '24-hour', label: '24-hour' }
            ]}
          />
        </div>
      </Card>
    </div>
  );

  const renderIntegrations = () => (
    <div className="space-y-6">
      <Card title="Job Board Integrations" padding="lg">
        <div className="space-y-4">
          {[
            { name: 'LinkedIn', key: 'linkedinEnabled', description: 'Post jobs and source candidates from LinkedIn' },
            { name: 'Indeed', key: 'indeedEnabled', description: 'Automatically post jobs to Indeed' },
            { name: 'Glassdoor', key: 'glassdoorEnabled', description: 'Sync company reviews and job postings' }
          ].map((integration) => (
            <div key={integration.key} className="flex justify-between items-center p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Icon name="link" size="sm" className="text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">{integration.name}</p>
                  <p className="text-sm text-gray-600">{integration.description}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Badge variant={integrationSettings[integration.key as keyof typeof integrationSettings] ? 'success' : 'default'}>
                  {integrationSettings[integration.key as keyof typeof integrationSettings] ? 'Connected' : 'Disconnected'}
                </Badge>
                <Button variant="outline" size="sm">
                  {integrationSettings[integration.key as keyof typeof integrationSettings] ? 'Configure' : 'Connect'}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </Card>

      <Card title="Communication Tools" padding="lg">
        <div className="space-y-4">
          <div className="flex justify-between items-center p-4 border rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Icon name="message" size="sm" className="text-purple-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Slack</p>
                <p className="text-sm text-gray-600">Get notifications in your Slack workspace</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="success">Connected</Badge>
              <Button variant="outline" size="sm">Configure</Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings();
      case 'integrations':
        return renderIntegrations();
      case 'notifications':
        return (
          <Card title="Notification Preferences" padding="lg">
            <p className="text-gray-600">Notification settings would go here</p>
          </Card>
        );
      case 'security':
        return (
          <Card title="Security Settings" padding="lg">
            <p className="text-gray-600">Security settings would go here</p>
          </Card>
        );
      case 'billing':
        return (
          <Card title="Billing & Subscription" padding="lg">
            <p className="text-gray-600">Billing settings would go here</p>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Manage your application preferences and integrations</p>
        </div>
        <Button variant="primary" onClick={handleSaveSettings}>
          Save Changes
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="lg:col-span-1">
          <Card padding="sm">
            <nav className="space-y-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Icon 
                    name={tab.icon} 
                    size="sm" 
                    className={activeTab === tab.id ? 'text-blue-700' : 'text-gray-500'} 
                  />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </Card>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}
